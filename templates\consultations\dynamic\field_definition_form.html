{% extends 'base/base.html' %}

{% block title %}
{% if is_edit %}تعديل تعريف حقل{% else %}إضافة تعريف حقل جديد{% endif %} - هيأة المنافذ الحدودية
{% endblock %}

{% block page_title %}
{% if is_edit %}تعديل تعريف حقل{% else %}إضافة تعريف حقل جديد{% endif %}
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-3">
        <div class="col-md-12">
            <a href="{% url 'consultations:field_definition_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة تعريفات الحقول
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% if is_edit %}تعديل تعريف حقل{% else %}إضافة تعريف حقل جديد{% endif %}</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        تعريفات الحقول تسمح لك بتخصيص نموذج الكفالة حسب احتياجاتك.
                    </div>

                    <form method="post">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="name" class="form-label">اسم الحقل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{% if field %}{{ field.name }}{% endif %}" required>
                        </div>

                        {% if not is_edit %}
                        <div class="mb-3">
                            <label for="field_key" class="form-label">مفتاح الحقل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="field_key" name="field_key" value="{% if field %}{{ field.field_key }}{% endif %}" required>
                            <div class="form-text">يجب أن يكون مفتاح الحقل فريداً ويتكون من أحرف إنجليزية وأرقام وشرطات سفلية فقط (مثال: guarantor_name)</div>
                        </div>

                        <div class="mb-3">
                            <label for="field_type" class="form-label">نوع الحقل <span class="text-danger">*</span></label>
                            <select class="form-select" id="field_type" name="field_type" required>
                                <option value="" {% if not field %}selected{% endif %} disabled>اختر نوع الحقل</option>
                                <option value="text" {% if field and field.field_type == 'text' %}selected{% endif %}>نص</option>
                                <option value="number" {% if field and field.field_type == 'number' %}selected{% endif %}>رقم</option>
                                <option value="date" {% if field and field.field_type == 'date' %}selected{% endif %}>تاريخ</option>
                                <option value="boolean" {% if field and field.field_type == 'boolean' %}selected{% endif %}>نعم/لا</option>
                                <option value="select" {% if field and field.field_type == 'select' %}selected{% endif %}>قائمة منسدلة</option>
                                <option value="multi_select" {% if field and field.field_type == 'multi_select' %}selected{% endif %}>اختيار متعدد</option>
                            </select>
                        </div>

                        <div class="mb-3" id="options_container" style="display: none;">
                            <label for="options" class="form-label">خيارات <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="options" name="options" rows="5">{% if field %}{{ field.options }}{% endif %}</textarea>
                            <div class="form-text">اكتب كل خيار في سطر منفصل. مثال:
الخيار الأول
الخيار الثاني
الخيار الثالث</div>
                        </div>
                        {% else %}
                        <div class="mb-3">
                            <label class="form-label">مفتاح الحقل</label>
                            <input type="text" class="form-control" value="{{ field.field_key }}" disabled>
                            <input type="hidden" name="field_key" value="{{ field.field_key }}">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">نوع الحقل</label>
                            <input type="text" class="form-control" value="{{ field.get_field_type_display }}" disabled>
                            <input type="hidden" name="field_type" value="{{ field.field_type }}">
                            <div class="form-text">لا يمكن تغيير نوع الحقل بعد إنشائه.</div>
                        </div>
                        {% endif %}

                        <div class="mb-3">
                            <label for="section" class="form-label">القسم <span class="text-danger">*</span></label>
                            <select class="form-select" id="section" name="section" required>
                                <option value="basic" {% if field and field.section == 'basic' %}selected{% endif %}>المعلومات الأساسية</option>
                                <option value="additional" {% if field and field.section == 'additional' %}selected{% endif %}>معلومات إضافية</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="display_order" class="form-label">ترتيب العرض</label>
                            <input type="number" class="form-control" id="display_order" name="display_order" value="{% if field %}{{ field.display_order }}{% else %}0{% endif %}" min="0">
                            <div class="form-text">يتم ترتيب الحقول تصاعدياً حسب هذه القيمة.</div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_required" name="is_required" {% if field and field.is_required %}checked{% endif %}>
                            <label class="form-check-label" for="is_required">حقل مطلوب</label>
                        </div>

                        {% if is_edit %}
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {% if field and field.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">فعال</label>
                            <div class="form-text">إلغاء تفعيل الحقل سيخفيه من نموذج الكفالة.</div>
                        </div>
                        {% endif %}

                        {% if not is_edit %}
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_system" name="is_system" {% if field and field.is_system %}checked{% endif %}>
                            <label class="form-check-label" for="is_system">حقل نظام</label>
                            <div class="form-text">حقول النظام لا يمكن حذفها.</div>
                        </div>
                        {% endif %}

                        <div class="text-center mt-4">
                            <a href="{% url 'consultations:field_definition_list' %}" class="btn btn-secondary me-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">
                                {% if is_edit %}حفظ التغييرات{% else %}إضافة الحقل{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-generate field_key from name
        const nameInput = document.getElementById('name');
        const fieldKeyInput = document.getElementById('field_key');
        const fieldTypeSelect = document.getElementById('field_type');
        const optionsContainer = document.getElementById('options_container');

        // Function to toggle options container visibility
        function toggleOptionsContainer() {
            if (fieldTypeSelect.value === 'select' || fieldTypeSelect.value === 'multi_select') {
                optionsContainer.style.display = 'block';
                document.getElementById('options').setAttribute('required', 'required');
            } else {
                optionsContainer.style.display = 'none';
                document.getElementById('options').removeAttribute('required');
            }
        }

        // Initialize options container visibility
        if (fieldTypeSelect) {
            toggleOptionsContainer();

            // Add event listener for field type changes
            fieldTypeSelect.addEventListener('change', toggleOptionsContainer);
        }

        if (nameInput && fieldKeyInput) {
            nameInput.addEventListener('input', function() {
                // Only auto-generate if field_key is empty
                if (!fieldKeyInput.value) {
                    // Convert to lowercase, replace spaces with underscores, remove special characters
                    const key = this.value.toLowerCase()
                        .replace(/\s+/g, '_')
                        .replace(/[^\w_]/g, '');

                    fieldKeyInput.value = key;
                }
            });
        }
    });
</script>
{% endblock %}
{% endblock %}
