# Generated by Django 5.2 on 2025-04-16 20:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lawsuits', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DynamicCouncil',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_closed', models.BooleanField(default=False, verbose_name='مغلق')),
                ('extended', models.BooleanField(default=False, verbose_name='تم التمديد')),
                ('extension_days', models.PositiveIntegerField(default=0, verbose_name='أيام التمديد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_dynamic_councils', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_dynamic_councils', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'مجلس ديناميكي',
                'verbose_name_plural': 'مجالس ديناميكية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DynamicCouncilDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('formation_letter', 'كتاب التشكيل'), ('closure_letter', 'كتاب الاغلاق'), ('extension_letter', 'كتاب التمديد'), ('other', 'أخرى')], max_length=20, verbose_name='نوع المستند')),
                ('document_file', models.FileField(upload_to='dynamic_council_documents/', verbose_name='الملف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('council', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='lawsuits.dynamiccouncil', verbose_name='المجلس')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_dynamic_council_documents', to=settings.AUTH_USER_MODEL, verbose_name='تم الرفع بواسطة')),
            ],
            options={
                'verbose_name': 'مستند المجلس الديناميكي',
                'verbose_name_plural': 'مستندات المجالس الديناميكية',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='DynamicCouncilMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='اسم العضو')),
                ('position', models.CharField(max_length=255, verbose_name='مقر العمل')),
                ('is_head', models.BooleanField(default=False, verbose_name='رئيس اللجنة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('council', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='members', to='lawsuits.dynamiccouncil', verbose_name='المجلس')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_dynamic_council_members', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'عضو مجلس ديناميكي',
                'verbose_name_plural': 'أعضاء المجالس الديناميكية',
            },
        ),
        migrations.CreateModel(
            name='FieldDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='اسم الحقل')),
                ('field_key', models.CharField(max_length=100, unique=True, verbose_name='مفتاح الحقل')),
                ('field_type', models.CharField(choices=[('text', 'نص'), ('number', 'رقم'), ('date', 'تاريخ'), ('boolean', 'نعم/لا'), ('select', 'قائمة منسدلة'), ('multi_select', 'اختيار متعدد')], max_length=20, verbose_name='نوع الحقل')),
                ('section', models.CharField(choices=[('basic', 'المعلومات الأساسية'), ('additional', 'معلومات إضافية')], default='basic', max_length=20, verbose_name='القسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الحقل')),
                ('options', models.TextField(blank=True, null=True, verbose_name='خيارات الحقل (للقوائم المنسدلة)')),
                ('is_required', models.BooleanField(default=False, verbose_name='مطلوب')),
                ('is_system', models.BooleanField(default=False, verbose_name='حقل نظام')),
                ('is_active', models.BooleanField(default=True, verbose_name='فعال')),
                ('display_order', models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_council_fields', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'تعريف حقل المجلس',
                'verbose_name_plural': 'تعريفات حقول المجالس',
                'ordering': ['section', 'display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='FieldValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.TextField(verbose_name='القيمة')),
                ('council', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='field_values', to='lawsuits.dynamiccouncil', verbose_name='المجلس')),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='council_values', to='lawsuits.fielddefinition', verbose_name='الحقل')),
            ],
            options={
                'verbose_name': 'قيمة حقل المجلس',
                'verbose_name_plural': 'قيم حقول المجالس',
                'unique_together': {('council', 'field')},
            },
        ),
    ]
