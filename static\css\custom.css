/* Custom CSS for the Legal Department Management System */

/* Arabic Font */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background-color: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
    background-color: #343a40;
    min-height: 100vh;
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 100;
    padding: 0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-sticky {
    position: sticky;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: 1rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.75);
    padding: 0.75rem 1rem;
    border-right: 3px solid transparent;
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    border-right: 3px solid #007bff;
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: rgba(0, 123, 255, 0.25);
    border-right: 3px solid #007bff;
}

.sidebar .nav-link i {
    margin-left: 0.5rem;
}

/* Main Content Styles */
.main-content {
    margin-right: 250px;
    padding: 20px;
}

/* Card Styles */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0;
    font-weight: 500;
}

/* Button Styles */
.btn {
    border-radius: 0.25rem;
    font-weight: 500;
}

/* Table Styles */
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Form Styles */
.form-label {
    font-weight: 500;
}

.form-control, .form-select {
    border-radius: 0.25rem;
}

/* Dashboard Stats Cards */
.stats-card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.3s;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card .icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    padding: 0.25rem 0.5rem;
    border-radius: 50%;
    font-size: 0.75rem;
}

/* Custom Font Sizes */
.fs-7 {
    font-size: 0.875rem;
}

.fs-8 {
    font-size: 0.75rem;
}

/* Custom Colors */
.bg-light-primary {
    background-color: rgba(0, 123, 255, 0.1);
}

.bg-light-success {
    background-color: rgba(40, 167, 69, 0.1);
}

.bg-light-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.bg-light-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .sidebar {
        position: static;
        min-height: auto;
    }
    
    .main-content {
        margin-right: 0;
    }
}

/* Print Styles */
@media print {
    .sidebar, .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0;
        padding: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
