from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

class Council(models.Model):
    """Model for councils and committees"""
    COUNCIL_TYPES = (
        ('investigation_council', 'مجلس تحقيقي'),
        ('investigation_committee', 'لجنة تحقيقية'),
        ('joint_committee', 'لجنة مشتركة'),
    )

    council_type = models.CharField(max_length=30, choices=COUNCIL_TYPES, verbose_name="نوع المجلس")
    formation_order = models.CharField(max_length=100, verbose_name="امر التشكيل")
    formation_date = models.DateField(verbose_name="تاريخ امر التشكيل")
    port_name = models.CharField(max_length=255, verbose_name="اسم المنفذ أو الدائرة")
    duration = models.PositiveIntegerField(verbose_name="المدة المحددة لإنهاء اللجنة (بالأيام)")
    file_name = models.CharField(max_length=255, blank=True, null=True, verbose_name="اسم ورقم اضبارة الحفظ")
    notes = models.TextField(blank=True, null=True, verbose_name="الملاحظات")
    subject = models.TextField(verbose_name="موضوع اللجنة/ المجلس التحقيقي")
    last_action = models.TextField(blank=True, null=True, verbose_name="اخر اجراء")
    is_closed = models.BooleanField(default=False, verbose_name="مغلق")
    closure_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الإغلاق")
    extended = models.BooleanField(default=False, verbose_name="تم التمديد")
    extension_days = models.PositiveIntegerField(default=0, verbose_name="أيام التمديد")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_councils', verbose_name="تم الإنشاء بواسطة")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='updated_councils', verbose_name="تم التحديث بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مجلس/لجنة"
        verbose_name_plural = "المجالس/اللجان"
        ordering = ['-formation_date']

    def __str__(self):
        return f"{self.get_council_type_display()} - {self.formation_order} - {self.formation_date}"

    def is_expired(self):
        """Check if the council has expired"""
        if self.is_closed:
            return False

        end_date = self.formation_date
        if self.extended:
            # Add the original duration plus extension days
            total_days = self.duration + self.extension_days
            end_date = self.formation_date + timezone.timedelta(days=total_days)
        else:
            # Add just the original duration
            end_date = self.formation_date + timezone.timedelta(days=self.duration)

        return timezone.now().date() > end_date

    def days_remaining(self):
        """Calculate days remaining until expiry"""
        if self.is_closed:
            return 0

        end_date = self.formation_date
        if self.extended:
            total_days = self.duration + self.extension_days
            end_date = self.formation_date + timezone.timedelta(days=total_days)
        else:
            end_date = self.formation_date + timezone.timedelta(days=self.duration)

        remaining = (end_date - timezone.now().date()).days
        return max(0, remaining)

class CouncilMember(models.Model):
    """Model for council members"""
    council = models.ForeignKey(Council, on_delete=models.CASCADE, related_name='members', verbose_name="المجلس/اللجنة")
    name = models.CharField(max_length=255, verbose_name="اسم العضو")
    position = models.CharField(max_length=255, verbose_name="مقر العمل")
    is_head = models.BooleanField(default=False, verbose_name="رئيس اللجنة")

    class Meta:
        verbose_name = "عضو مجلس/لجنة"
        verbose_name_plural = "أعضاء المجالس/اللجان"

    def __str__(self):
        return f"{self.name} - {self.position}"

class CouncilDocument(models.Model):
    """Model for council documents"""
    DOCUMENT_TYPES = (
        ('formation_letter', 'كتاب التشكيل'),
        ('closure_letter', 'كتاب الاغلاق'),
        ('extension_letter', 'كتاب التمديد'),
        ('other', 'أخرى'),
    )

    council = models.ForeignKey(Council, on_delete=models.CASCADE, related_name='documents', verbose_name="المجلس/اللجنة")
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES, verbose_name="نوع المستند")
    document_file = models.FileField(upload_to='council_documents/', verbose_name="الملف")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='uploaded_council_documents', verbose_name="تم الرفع بواسطة")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الرفع")

    class Meta:
        verbose_name = "مستند المجلس/اللجنة"
        verbose_name_plural = "مستندات المجالس/اللجان"
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.get_document_type_display()} - {self.council}"
