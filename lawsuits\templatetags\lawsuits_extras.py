from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """Get an item from a dictionary by key"""
    return dictionary.get(key)

@register.filter
def get_council_field(council, field_key):
    """Get a field value from a council by field key"""
    if not council or isinstance(council, str):
        return ""

    # Handle direct attributes
    if hasattr(council, field_key):
        value = getattr(council, field_key)
        if callable(value):
            return value()
        return value

    # Handle field values
    try:
        field_value = council.field_values.filter(field__field_key=field_key).first()
        if field_value:
            return field_value.value
    except:
        pass

    # Try get_field_value method if it exists
    if hasattr(council, 'get_field_value'):
        return council.get_field_value(field_key)

    return ""

@register.filter
def split_string(value, delimiter=','):
    """Split a string by delimiter"""
    if value:
        return value.split(delimiter)
    return []
