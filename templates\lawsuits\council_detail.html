{% extends 'base/base.html' %}

{% block title %}تفاصيل المجلس/اللجنة - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}تفاصيل المجلس/اللجنة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{% url 'lawsuits:council_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i> العودة إلى القائمة
                    </a>
                </div>
                <div>
                    {% if not council.is_closed %}
                    <a href="{% url 'lawsuits:council_close' council.id %}" class="btn btn-secondary me-2">
                        <i class="fas fa-lock me-2"></i> إغلاق المجلس/اللجنة
                    </a>
                    {% if not council.extended %}
                    <a href="{% url 'lawsuits:council_extend' council.id %}" class="btn btn-info me-2">
                        <i class="fas fa-calendar-plus me-2"></i> تمديد المجلس/اللجنة
                    </a>
                    {% endif %}
                    {% endif %}
                    <a href="{% url 'lawsuits:council_edit' council.id %}" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-2"></i> تعديل
                    </a>
                    <a href="{% url 'lawsuits:upload_council_document' council.id %}" class="btn btn-info me-2">
                        <i class="fas fa-file-upload me-2"></i> رفع مستند
                    </a>
                    <a href="{% url 'lawsuits:council_delete' council.id %}" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i> حذف
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    {% if not council.is_closed and is_expired %}
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>تنبيه!</strong> هذا المجلس/اللجنة متجاوز للمدة المحددة. يرجى إغلاقه أو تمديده.
    </div>
    {% endif %}
    
    <div class="row">
        <div class="col-md-8">
            <!-- Council Details -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معلومات المجلس/اللجنة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">نوع المجلس/اللجنة:</label>
                            <p>{{ council.get_council_type_display }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">أمر التشكيل:</label>
                            <p>{{ council.formation_order }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تاريخ التشكيل:</label>
                            <p>{{ council.formation_date }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">المنفذ/الدائرة:</label>
                            <p>{{ council.port_name }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">المدة المحددة (أيام):</label>
                            <p>{{ council.duration }}{% if council.extended %} + {{ council.extension_days }} (تم التمديد){% endif %}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الحالة:</label>
                            <p>
                                {% if council.is_closed %}
                                    <span class="badge bg-secondary">مغلق</span>
                                    {% if council.closure_date %}
                                    <small class="text-muted">(تاريخ الإغلاق: {{ council.closure_date }})</small>
                                    {% endif %}
                                {% else %}
                                    {% if is_expired %}
                                        <span class="badge bg-danger">متجاوز للمدة</span>
                                    {% else %}
                                        <span class="badge bg-success">فعال</span>
                                        <small class="text-muted">(متبقي {{ days_remaining }} يوم)</small>
                                    {% endif %}
                                {% endif %}
                            </p>
                        </div>
                        {% if council.file_name %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">اسم ورقم اضبارة الحفظ:</label>
                            <p>{{ council.file_name }}</p>
                        </div>
                        {% endif %}
                    </div>
                    
                    <hr>
                    <h6 class="mb-3">موضوع المجلس/اللجنة:</h6>
                    <p>{{ council.subject }}</p>
                    
                    {% if council.last_action %}
                    <hr>
                    <h6 class="mb-3">آخر إجراء:</h6>
                    <p>{{ council.last_action }}</p>
                    {% endif %}
                    
                    {% if council.notes %}
                    <hr>
                    <h6 class="mb-3">ملاحظات:</h6>
                    <p>{{ council.notes }}</p>
                    {% endif %}
                    
                    <hr>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تم الإنشاء بواسطة:</label>
                            <p>{{ council.created_by.get_full_name }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                            <p>{{ council.created_at }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تم التحديث بواسطة:</label>
                            <p>{{ council.updated_by.get_full_name }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تاريخ التحديث:</label>
                            <p>{{ council.updated_at }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Council Members -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أعضاء المجلس/اللجنة</h5>
                    <a href="{% url 'lawsuits:council_members' council.id %}" class="text-white">
                        <i class="fas fa-users-cog"></i> إدارة الأعضاء
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>مقر العمل</th>
                                    <th>المنصب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for member in members %}
                                <tr>
                                    <td>{{ member.name }}</td>
                                    <td>{{ member.position }}</td>
                                    <td>
                                        {% if member.is_head %}
                                        <span class="badge bg-primary">رئيس اللجنة</span>
                                        {% else %}
                                        <span class="badge bg-secondary">عضو</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">لا يوجد أعضاء مسجلين</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Documents -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">المستندات</h5>
                    <a href="{% url 'lawsuits:upload_council_document' council.id %}" class="text-white">
                        <i class="fas fa-plus-circle"></i> إضافة مستند
                    </a>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        {% for document in documents %}
                        <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ document.get_document_type_display }}</h6>
                                <p class="mb-1 text-muted small">{{ document.description }}</p>
                                <small class="text-muted">{{ document.uploaded_at }}</small>
                            </div>
                            <div>
                                <a href="{{ document.document_file.url }}" class="btn btn-sm btn-primary" target="_blank" data-bs-toggle="tooltip" title="تنزيل">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-3">
                            <p class="mb-0">لا توجد مستندات</p>
                            <a href="{% url 'lawsuits:upload_council_document' council.id %}" class="btn btn-sm btn-primary mt-2">
                                <i class="fas fa-plus-circle me-2"></i> إضافة مستند
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
