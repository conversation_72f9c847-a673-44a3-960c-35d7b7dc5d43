# Generated by Django 5.2 on 2025-04-09 20:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Council',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('council_type', models.CharField(choices=[('investigation_council', 'مجلس تحقيقي'), ('investigation_committee', 'لجنة تحقيقية'), ('joint_committee', 'لجنة مشتركة')], max_length=30, verbose_name='نوع المجلس')),
                ('formation_order', models.CharField(max_length=100, verbose_name='امر التشكيل')),
                ('formation_date', models.Date<PERSON>ield(verbose_name='تاريخ امر التشكيل')),
                ('port_name', models.CharField(max_length=255, verbose_name='اسم المنفذ أو الدائرة')),
                ('duration', models.PositiveIntegerField(verbose_name='المدة المحددة لإنهاء اللجنة (بالأيام)')),
                ('file_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='اسم ورقم اضبارة الحفظ')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='الملاحظات')),
                ('subject', models.TextField(verbose_name='موضوع اللجنة/ المجلس التحقيقي')),
                ('last_action', models.TextField(blank=True, null=True, verbose_name='اخر اجراء')),
                ('is_closed', models.BooleanField(default=False, verbose_name='مغلق')),
                ('closure_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإغلاق')),
                ('extended', models.BooleanField(default=False, verbose_name='تم التمديد')),
                ('extension_days', models.PositiveIntegerField(default=0, verbose_name='أيام التمديد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_councils', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_councils', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'مجلس/لجنة',
                'verbose_name_plural': 'المجالس/اللجان',
                'ordering': ['-formation_date'],
            },
        ),
        migrations.CreateModel(
            name='CouncilDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('formation_letter', 'كتاب التشكيل'), ('closure_letter', 'كتاب الاغلاق'), ('extension_letter', 'كتاب التمديد'), ('other', 'أخرى')], max_length=20, verbose_name='نوع المستند')),
                ('document_file', models.FileField(upload_to='council_documents/', verbose_name='الملف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('council', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='lawsuits.council', verbose_name='المجلس/اللجنة')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_council_documents', to=settings.AUTH_USER_MODEL, verbose_name='تم الرفع بواسطة')),
            ],
            options={
                'verbose_name': 'مستند المجلس/اللجنة',
                'verbose_name_plural': 'مستندات المجالس/اللجان',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='CouncilMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='اسم العضو')),
                ('position', models.CharField(max_length=255, verbose_name='مقر العمل')),
                ('is_head', models.BooleanField(default=False, verbose_name='رئيس اللجنة')),
                ('council', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='members', to='lawsuits.council', verbose_name='المجلس/اللجنة')),
            ],
            options={
                'verbose_name': 'عضو مجلس/لجنة',
                'verbose_name_plural': 'أعضاء المجالس/اللجان',
            },
        ),
    ]
