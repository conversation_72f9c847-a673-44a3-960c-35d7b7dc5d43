{% extends 'base/base.html' %}

{% block title %}قسم الاستشارات والكفالات - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}قسم الاستشارات والكفالات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">إجمالي الكفالات</h6>
                            <h2 class="mb-0">{{ guarantee_count }}</h2>
                        </div>
                        <i class="fas fa-file-contract fa-3x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <span>عرض التفاصيل</span>
                    <a href="{% url 'consultations:guarantee_list' %}" class="text-white">
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">الكفالات الفعالة</h6>
                            <h2 class="mb-0">{{ active_guarantee_count }}</h2>
                        </div>
                        <i class="fas fa-check-circle fa-3x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <span>عرض التفاصيل</span>
                    <a href="{% url 'consultations:guarantee_list' %}?status=active" class="text-white">
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">الكفالات المنتهية</h6>
                            <h2 class="mb-0">{{ expired_guarantee_count }}</h2>
                        </div>
                        <i class="fas fa-exclamation-triangle fa-3x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <span>عرض التفاصيل</span>
                    <a href="{% url 'consultations:guarantee_list' %}?status=expired" class="text-white">
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">إجراءات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>نظام الكفالات</strong> يتيح لك إدارة الكفالات وتخصيص الحقول بشكل كامل من خلال واجهة المستخدم.
                            </div>
                        </div>
                    </div>

                    <h5 class="mb-3">إجراءات سريعة</h5>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'consultations:dynamic_guarantee_create' %}" class="btn btn-primary w-100">
                                <i class="fas fa-plus-circle me-2"></i> إضافة كفالة جديدة
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'consultations:dynamic_guarantee_list' %}" class="btn btn-info w-100">
                                <i class="fas fa-list me-2"></i> عرض جميع الكفالات
                            </a>
                        </div>
                        {% if user.is_staff %}
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'consultations:field_definition_list' %}" class="btn btn-success w-100">
                                <i class="fas fa-cog me-2"></i> إدارة تعريفات الحقول
                            </a>
                        </div>
                        {% endif %}
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'dashboard:reports' %}" class="btn btn-warning w-100">
                                <i class="fas fa-chart-bar me-2"></i> تقارير الكفالات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Guarantees -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث الكفالات</h5>
                    <a href="{% url 'consultations:guarantee_list' %}" class="text-white">
                        <i class="fas fa-external-link-alt"></i> عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الكفيل</th>
                                    <th>اسم المكفول</th>
                                    <th>مبلغ الكفالة</th>
                                    <th>المصرف المانح</th>
                                    <th>تاريخ الإصدار</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for guarantee in recent_guarantees %}
                                <tr>
                                    <td>{{ guarantee.guarantor_name }}</td>
                                    <td>{{ guarantee.guaranteed_name }}</td>
                                    <td>{{ guarantee.guarantee_amount }}</td>
                                    <td>{{ guarantee.bank_name }}</td>
                                    <td>{{ guarantee.issue_date }}</td>
                                    <td>{{ guarantee.expiry_date }}</td>
                                    <td>
                                        {% if guarantee.is_active %}
                                            {% if guarantee.expiry_date < today %}
                                                <span class="badge bg-danger">منتهية</span>
                                            {% else %}
                                                <span class="badge bg-success">فعالة</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-secondary">غير فعالة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'consultations:guarantee_detail' guarantee.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'consultations:guarantee_edit' guarantee.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد كفالات</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
