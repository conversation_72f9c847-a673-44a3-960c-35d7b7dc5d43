from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext as _
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.core.paginator import Paginator
from django.utils import timezone

from .models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eeDocument, GuaranteeField, GuaranteeFieldValue
from core.models import AuditLog

# Redirect views for legacy URLs
@login_required
def redirect_to_dynamic_guarantees(request, guarantee_id=None):
    """Redirect to dynamic guarantees list"""
    messages.info(request, _('تم تحديث النظام. تم توجيهك إلى النظام الجديد.'))
    return redirect('consultations:dynamic_guarantee_list')

@login_required
def redirect_to_dynamic_guarantee_create(request):
    """Redirect to dynamic guarantee create"""
    messages.info(request, _('تم تحديث النظام. تم توجيهك إلى النظام الجديد.'))
    return redirect('consultations:dynamic_guarantee_create')

@login_required
def redirect_to_field_definitions(request, field_id=None):
    """Redirect to field definitions list"""
    messages.info(request, _('تم تحديث النظام. تم توجيهك إلى النظام الجديد.'))
    return redirect('consultations:field_definition_list')

@login_required
def redirect_to_field_definition_create(request):
    """Redirect to field definition create"""
    messages.info(request, _('تم تحديث النظام. تم توجيهك إلى النظام الجديد.'))
    return redirect('consultations:field_definition_create')

@login_required
def index(request):
    """Consultations home page"""
    # Get counts from dynamic guarantees
    from .dynamic_models import DynamicGuarantee, FieldDefinition

    guarantee_count = DynamicGuarantee.objects.count()
    active_guarantee_count = DynamicGuarantee.objects.filter(is_active=True).count()

    # Get recent guarantees
    recent_guarantees = DynamicGuarantee.objects.all().order_by('-created_at')[:5]

    context = {
        'guarantee_count': guarantee_count,
        'active_guarantee_count': active_guarantee_count,
        'recent_guarantees': recent_guarantees,
    }

    return render(request, 'consultations/index.html', context)

@login_required
def guarantee_list(request):
    """List all guarantees"""
    # Get filter parameters
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', 'all')
    bank_filter = request.GET.get('bank', '')
    start_date = request.GET.get('start_date', '')
    end_date = request.GET.get('end_date', '')

    # Base queryset
    guarantees = Guarantee.objects.all()

    # Apply filters
    if search_query:
        guarantees = guarantees.filter(
            Q(guarantor_name__icontains=search_query) |
            Q(guaranteed_name__icontains=search_query) |
            Q(bank_name__icontains=search_query)
        )

    if status_filter == 'active':
        guarantees = guarantees.filter(is_active=True)
    elif status_filter == 'inactive':
        guarantees = guarantees.filter(is_active=False)
    elif status_filter == 'expired':
        guarantees = guarantees.filter(
            is_active=True,
            expiry_date__lt=timezone.now().date()
        )

    if bank_filter:
        guarantees = guarantees.filter(bank_name=bank_filter)

    if start_date:
        guarantees = guarantees.filter(issue_date__gte=start_date)

    if end_date:
        guarantees = guarantees.filter(expiry_date__lte=end_date)

    # Order by
    order_by = request.GET.get('order_by', '-issue_date')
    guarantees = guarantees.order_by(order_by)

    # Get unique bank names for filter dropdown
    bank_names = Guarantee.objects.values_list('bank_name', flat=True).distinct()

    # Pagination
    paginator = Paginator(guarantees, 10)  # Show 10 guarantees per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'bank_filter': bank_filter,
        'bank_names': bank_names,
        'start_date': start_date,
        'end_date': end_date,
        'order_by': order_by,
    }

    return render(request, 'consultations/guarantee_list.html', context)

@login_required
def guarantee_detail(request, guarantee_id):
    """Display guarantee details"""
    guarantee = get_object_or_404(Guarantee, id=guarantee_id)
    documents = guarantee.documents.all()
    field_values = guarantee.field_values.all()

    context = {
        'guarantee': guarantee,
        'documents': documents,
        'field_values': field_values,
    }

    return render(request, 'consultations/guarantee_detail.html', context)

@login_required
def guarantee_create(request):
    """Create a new guarantee"""
    # Get all active custom fields
    custom_fields = GuaranteeField.objects.filter(is_active=True)

    if request.method == 'POST':
        # Get form data
        guarantor_name = request.POST.get('guarantor_name')
        guaranteed_name = request.POST.get('guaranteed_name')
        guarantee_duration = request.POST.get('guarantee_duration')
        guarantee_amount = request.POST.get('guarantee_amount')
        bank_name = request.POST.get('bank_name')
        issue_date = request.POST.get('issue_date')
        expiry_date = request.POST.get('expiry_date')

        # Create guarantee
        guarantee = Guarantee.objects.create(
            guarantor_name=guarantor_name,
            guaranteed_name=guaranteed_name,
            guarantee_duration=guarantee_duration,
            guarantee_amount=guarantee_amount,
            bank_name=bank_name,
            issue_date=issue_date,
            expiry_date=expiry_date,
            is_active=True,
            created_by=request.user,
            updated_by=request.user,
        )

        # Handle custom fields
        for field in custom_fields:
            field_value = request.POST.get(f'field_{field.id}')
            if field_value:
                GuaranteeFieldValue.objects.create(
                    guarantee=guarantee,
                    field=field,
                    value=field_value
                )

        # Handle guarantee letter document
        if 'guarantee_letter' in request.FILES:
            GuaranteeDocument.objects.create(
                guarantee=guarantee,
                document_type='guarantee_letter',
                document_file=request.FILES['guarantee_letter'],
                description=_('كتاب الكفالة'),
                uploaded_by=request.user
            )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='Guarantee',
            object_id=guarantee.id,
            description=_('تم إنشاء كفالة جديدة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إنشاء الكفالة بنجاح'))
        return redirect('consultations:guarantee_detail', guarantee_id=guarantee.id)

    context = {
        'custom_fields': custom_fields,
    }

    return render(request, 'consultations/guarantee_form.html', context)

@login_required
def guarantee_edit(request, guarantee_id):
    """Edit an existing guarantee"""
    guarantee = get_object_or_404(Guarantee, id=guarantee_id)
    custom_fields = GuaranteeField.objects.filter(is_active=True)
    field_values = {fv.field_id: fv.value for fv in guarantee.field_values.all()}

    if request.method == 'POST':
        # Update guarantee
        guarantee.guarantor_name = request.POST.get('guarantor_name')
        guarantee.guaranteed_name = request.POST.get('guaranteed_name')
        guarantee.guarantee_duration = request.POST.get('guarantee_duration')
        guarantee.guarantee_amount = request.POST.get('guarantee_amount')
        guarantee.bank_name = request.POST.get('bank_name')
        guarantee.issue_date = request.POST.get('issue_date')
        guarantee.expiry_date = request.POST.get('expiry_date')
        guarantee.is_active = 'is_active' in request.POST
        guarantee.updated_by = request.user
        guarantee.save()

        # Update custom fields
        for field in custom_fields:
            field_value = request.POST.get(f'field_{field.id}')
            if field_value:
                GuaranteeFieldValue.objects.update_or_create(
                    guarantee=guarantee,
                    field=field,
                    defaults={'value': field_value}
                )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='Guarantee',
            object_id=guarantee.id,
            description=_('تم تحديث الكفالة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث الكفالة بنجاح'))
        return redirect('consultations:guarantee_detail', guarantee_id=guarantee.id)

    context = {
        'guarantee': guarantee,
        'custom_fields': custom_fields,
        'field_values': field_values,
        'is_edit': True,
    }

    return render(request, 'consultations/guarantee_form.html', context)

@login_required
def guarantee_delete(request, guarantee_id):
    """Delete a guarantee"""
    guarantee = get_object_or_404(Guarantee, id=guarantee_id)

    if request.method == 'POST':
        # Log the action before deletion
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            model_name='Guarantee',
            object_id=guarantee.id,
            description=_('تم حذف الكفالة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        guarantee.delete()
        messages.success(request, _('تم حذف الكفالة بنجاح'))
        return redirect('consultations:guarantee_list')

    context = {
        'guarantee': guarantee,
    }

    return render(request, 'consultations/guarantee_confirm_delete.html', context)

@login_required
def upload_guarantee_document(request, guarantee_id):
    """Upload a document for a guarantee"""
    guarantee = get_object_or_404(Guarantee, id=guarantee_id)

    if request.method == 'POST':
        document_type = request.POST.get('document_type')
        description = request.POST.get('description')
        document_file = request.FILES.get('document_file')

        if document_file:
            document = GuaranteeDocument.objects.create(
                guarantee=guarantee,
                document_type=document_type,
                document_file=document_file,
                description=description,
                uploaded_by=request.user
            )

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='create',
                model_name='GuaranteeDocument',
                object_id=document.id,
                description=_('تم رفع مستند جديد للكفالة'),
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, _('تم رفع المستند بنجاح'))
        else:
            messages.error(request, _('يرجى اختيار ملف لرفعه'))

        return redirect('consultations:guarantee_detail', guarantee_id=guarantee.id)

    context = {
        'guarantee': guarantee,
    }

    return render(request, 'consultations/upload_document.html', context)

@login_required
def field_list(request):
    """List all custom fields"""
    fields = GuaranteeField.objects.all().order_by('name')

    context = {
        'fields': fields,
    }

    return render(request, 'consultations/field_list.html', context)

@login_required
def field_create(request):
    """Create a new custom field"""
    if request.method == 'POST':
        name = request.POST.get('name')
        field_type = request.POST.get('field_type')
        is_required = 'is_required' in request.POST

        field = GuaranteeField.objects.create(
            name=name,
            field_type=field_type,
            is_required=is_required,
            is_active=True,
            created_by=request.user
        )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='GuaranteeField',
            object_id=field.id,
            description=_('تم إنشاء حقل جديد'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إنشاء الحقل بنجاح'))
        return redirect('consultations:field_list')

    return render(request, 'consultations/field_form.html')

@login_required
def field_edit(request, field_id):
    """Edit an existing custom field"""
    field = get_object_or_404(GuaranteeField, id=field_id)

    if request.method == 'POST':
        field.name = request.POST.get('name')
        field.field_type = request.POST.get('field_type')
        field.is_required = 'is_required' in request.POST
        field.is_active = 'is_active' in request.POST
        field.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='GuaranteeField',
            object_id=field.id,
            description=_('تم تحديث الحقل'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث الحقل بنجاح'))
        return redirect('consultations:field_list')

    context = {
        'field': field,
        'is_edit': True,
    }

    return render(request, 'consultations/field_form.html', context)

@login_required
def field_delete(request, field_id):
    """Delete a custom field"""
    field = get_object_or_404(GuaranteeField, id=field_id)

    if request.method == 'POST':
        # Check if field is used in any guarantees
        if GuaranteeFieldValue.objects.filter(field=field).exists():
            messages.error(request, _('لا يمكن حذف الحقل لأنه مستخدم في كفالات موجودة'))
            return redirect('consultations:field_list')

        # Log the action before deletion
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            model_name='GuaranteeField',
            object_id=field.id,
            description=_('تم حذف الحقل'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        field.delete()
        messages.success(request, _('تم حذف الحقل بنجاح'))
        return redirect('consultations:field_list')

    context = {
        'field': field,
    }

    return render(request, 'consultations/field_confirm_delete.html', context)
