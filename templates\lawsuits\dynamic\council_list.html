{% extends 'base/base.html' %}
{% load lawsuits_extras %}

{% block title %}المجالس/اللجان الديناميكية{% endblock %}

{% block page_title %}المجالس/اللجان الديناميكية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">المجالس/اللجان الديناميكية</h5>
                    <div>
                        <a href="{% url 'lawsuits:dynamic_council_create' %}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus-circle"></i> إضافة مجلس/لجنة جديدة
                        </a>
                        <a href="{% url 'lawsuits:field_definition_list' %}" class="btn btn-warning btn-sm ms-2">
                            <i class="fas fa-cogs"></i> إدارة تعريفات الحقول
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" placeholder="بحث..." value="{{ search_query }}">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="status">
                                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>جميع الحالات</option>
                                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>فعال</option>
                                    <option value="closed" {% if status_filter == 'closed' %}selected{% endif %}>مغلق</option>
                                    <option value="expired" {% if status_filter == 'expired' %}selected{% endif %}>متجاوز للمدة</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="council_type">
                                    <option value="">نوع المجلس/اللجنة</option>
                                    {% for council_type in council_types %}
                                    <option value="{{ council_type }}" {% if council_type_filter == council_type %}selected{% endif %}>{{ council_type }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control" name="start_date" placeholder="من تاريخ" value="{{ start_date }}">
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control" name="end_date" placeholder="إلى تاريخ" value="{{ end_date }}">
                            </div>
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter"></i> تصفية
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Councils Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>نوع المجلس/اللجنة</th>
                                    <th>أمر التشكيل</th>
                                    <th>تاريخ التشكيل</th>
                                    <th>المنفذ/الدائرة</th>
                                    <th>الموضوع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for council in page_obj %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ council|get_council_field:'council_type' }}</td>
                                    <td>{{ council|get_council_field:'formation_order' }}</td>
                                    <td>{{ council|get_council_field:'formation_date' }}</td>
                                    <td>{{ council|get_council_field:'port_name' }}</td>
                                    <td>{{ council|get_council_field:'subject' }}</td>
                                    <td>
                                        {% if council.is_closed %}
                                            <span class="badge bg-secondary">مغلق</span>
                                        {% else %}
                                            {% if council.is_expired %}
                                                <span class="badge bg-danger">متجاوز للمدة</span>
                                            {% else %}
                                                <span class="badge bg-success">فعال</span>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'lawsuits:dynamic_council_detail' council.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'lawsuits:dynamic_council_edit' council.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'lawsuits:dynamic_council_delete' council.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد مجالس/لجان</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if council_type_filter %}&council_type={{ council_type_filter }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if council_type_filter %}&council_type={{ council_type_filter }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if council_type_filter %}&council_type={{ council_type_filter }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}">{{ num }}</a>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if council_type_filter %}&council_type={{ council_type_filter }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if council_type_filter %}&council_type={{ council_type_filter }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
