from django.contrib import admin
from .models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>Field, GuaranteeFieldValue

# Import dynamic models
from .dynamic_models import FieldDefinition, DynamicGuarantee, FieldValue, DynamicGuaranteeDocument
from .dynamic_admin import FieldDefinitionAdmin, DynamicGuaranteeAdmin, FieldValueAdmin, DynamicGuaranteeDocumentAdmin

class GuaranteeDocumentInline(admin.TabularInline):
    model = GuaranteeDocument
    extra = 1

class GuaranteeFieldValueInline(admin.TabularInline):
    model = GuaranteeFieldValue
    extra = 1

@admin.register(Guarantee)
class GuaranteeAdmin(admin.ModelAdmin):
    list_display = ('guarantor_name', 'guaranteed_name', 'guarantee_amount', 'bank_name', 'issue_date', 'expiry_date', 'is_active')
    list_filter = ('is_active', 'bank_name', 'issue_date', 'expiry_date')
    search_fields = ('guarantor_name', 'guaranteed_name', 'bank_name')
    date_hierarchy = 'issue_date'
    readonly_fields = ('created_by', 'updated_by', 'created_at', 'updated_at')
    inlines = [GuaranteeDocumentInline, GuaranteeFieldValueInline]

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(GuaranteeDocument)
class GuaranteeDocumentAdmin(admin.ModelAdmin):
    list_display = ('guarantee', 'document_type', 'uploaded_by', 'uploaded_at')
    list_filter = ('document_type', 'uploaded_at')
    search_fields = ('guarantee__guarantor_name', 'guarantee__guaranteed_name', 'description')
    readonly_fields = ('uploaded_by', 'uploaded_at')

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(GuaranteeField)
class GuaranteeFieldAdmin(admin.ModelAdmin):
    list_display = ('name', 'field_type', 'is_required', 'is_active', 'created_by', 'created_at')
    list_filter = ('field_type', 'is_required', 'is_active')
    search_fields = ('name',)
    readonly_fields = ('created_by', 'created_at')

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
