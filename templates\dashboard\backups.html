{% extends 'base/base.html' %}

{% block title %}النسخ الاحتياطية - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}النسخ الاحتياطية{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-end">
                <a href="{% url 'dashboard:create_backup' %}" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-2"></i> إنشاء نسخة احتياطية جديدة
                </a>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">النسخ الاحتياطية المتوفرة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>الوصف</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>حجم الملف</th>
                                    <th>تم الإنشاء بواسطة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for backup in backups %}
                                <tr>
                                    <td>{{ backup.name }}</td>
                                    <td>{{ backup.description|default:"-" }}</td>
                                    <td>{{ backup.created_at }}</td>
                                    <td>{{ backup.file_size|filesizeformat }}</td>
                                    <td>{{ backup.created_by.get_full_name }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ backup.backup_file.url }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="تنزيل النسخة الاحتياطية">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <a href="{% url 'dashboard:restore_backup' backup.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="استعادة النسخة الاحتياطية">
                                                <i class="fas fa-undo"></i>
                                            </a>
                                            <a href="#" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف النسخة الاحتياطية">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">لا توجد نسخ احتياطية متوفرة</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
