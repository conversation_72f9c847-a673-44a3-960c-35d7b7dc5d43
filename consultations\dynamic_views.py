from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext as _
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.core.paginator import Paginator
from django.utils import timezone
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter
import datetime

from .dynamic_models import FieldDefinition, DynamicGuarantee, FieldValue, DynamicGuaranteeDocument
from core.models import AuditLog

from django.contrib.admin.views.decorators import staff_member_required

@login_required
def export_guarantees_to_excel(request):
    """Export guarantees to Excel"""
    # Get all active field definitions
    fields = FieldDefinition.objects.filter(is_active=True).order_by('section', 'display_order', 'name')

    # Get all guarantees
    guarantees = DynamicGuarantee.objects.all().order_by('-created_at')

    # Create a new workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "الكفالات"

    # Set RTL direction
    ws.sheet_view.rightToLeft = True

    # Define styles
    header_font = Font(bold=True, size=12)
    header_fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)

    # Add headers
    headers = ['#', 'الحالة', 'تاريخ الإنشاء']
    for field in fields:
        headers.append(field.name)

    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_num, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment

    # Add data
    for row_num, guarantee in enumerate(guarantees, 2):
        # Get field values for this guarantee
        field_values = {fv.field.field_key: fv.value for fv in guarantee.field_values.all().select_related('field')}

        # Add basic columns
        ws.cell(row=row_num, column=1, value=row_num-1)  # Row number

        # Status
        status = "فعالة" if guarantee.is_active else "غير فعالة"
        ws.cell(row=row_num, column=2, value=status)

        # Created date
        ws.cell(row=row_num, column=3, value=guarantee.created_at.strftime("%Y-%m-%d"))

        # Add field values
        for col_num, field in enumerate(fields, 4):
            value = field_values.get(field.field_key, "")

            # Format boolean values
            if field.field_type == 'boolean':
                value = "نعم" if value == "true" else "لا"

            # Format multi-select values
            elif field.field_type == 'multi_select' and value:
                value = value.replace(",", ", ")

            ws.cell(row=row_num, column=col_num, value=value)

    # Auto-adjust column widths
    for col_num in range(1, len(headers) + 1):
        ws.column_dimensions[get_column_letter(col_num)].width = 20

    # Create response
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename=guarantees_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'

    # Save workbook to response
    wb.save(response)

    # Log the action
    AuditLog.objects.create(
        user=request.user,
        action='export',
        model_name='DynamicGuarantee',
        object_id=0,
        description=_('تم تصدير الكفالات إلى ملف Excel'),
        ip_address=request.META.get('REMOTE_ADDR')
    )

    return response

@staff_member_required
def field_definition_list(request):
    """List all field definitions - only accessible to staff members"""
    fields = FieldDefinition.objects.all().order_by('section', 'display_order', 'name')

    # Group fields by section
    basic_fields = fields.filter(section='basic')
    additional_fields = fields.filter(section='additional')

    context = {
        'basic_fields': basic_fields,
        'additional_fields': additional_fields,
    }

    return render(request, 'consultations/dynamic/field_definition_list.html', context)

@staff_member_required
def field_definition_create(request):
    """Create a new field definition - only accessible to staff members"""
    if request.method == 'POST':
        name = request.POST.get('name')
        field_key = request.POST.get('field_key')
        field_type = request.POST.get('field_type')
        section = request.POST.get('section')
        is_required = 'is_required' in request.POST
        is_system = 'is_system' in request.POST
        display_order = request.POST.get('display_order', 0)
        options = None

        # Get options for select and multi_select fields
        if field_type in ['select', 'multi_select']:
            options = request.POST.get('options')

        # Validate field_key uniqueness
        if FieldDefinition.objects.filter(field_key=field_key).exists():
            messages.error(request, _('مفتاح الحقل موجود بالفعل'))
            return render(request, 'consultations/dynamic/field_definition_form.html', {
                'name': name,
                'field_key': field_key,
                'field_type': field_type,
                'section': section,
                'is_required': is_required,
                'is_system': is_system,
                'display_order': display_order,
            })

        field = FieldDefinition.objects.create(
            name=name,
            field_key=field_key,
            field_type=field_type,
            section=section,
            is_required=is_required,
            is_system=is_system,
            display_order=display_order,
            options=options,
            is_active=True,
            created_by=request.user,
            updated_by=request.user,
        )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='FieldDefinition',
            object_id=field.id,
            description=_('تم إنشاء تعريف حقل جديد'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إنشاء تعريف الحقل بنجاح'))
        return redirect('consultations:field_definition_list')

    context = {
        'is_edit': False,
    }

    return render(request, 'consultations/dynamic/field_definition_form.html', context)

@staff_member_required
def field_definition_edit(request, field_id):
    """Edit an existing field definition - only accessible to staff members"""
    field = get_object_or_404(FieldDefinition, id=field_id)

    if request.method == 'POST':
        field.name = request.POST.get('name')
        field.section = request.POST.get('section')
        field.is_required = 'is_required' in request.POST
        field.is_system = 'is_system' in request.POST
        field.is_active = 'is_active' in request.POST
        field.display_order = request.POST.get('display_order', 0)

        # Update options for select and multi_select fields
        if field.field_type in ['select', 'multi_select']:
            field.options = request.POST.get('options')

        field.updated_by = request.user
        field.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='FieldDefinition',
            object_id=field.id,
            description=_('تم تحديث تعريف الحقل'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث تعريف الحقل بنجاح'))
        return redirect('consultations:field_definition_list')

    context = {
        'field': field,
        'is_edit': True,
    }

    return render(request, 'consultations/dynamic/field_definition_form.html', context)

@staff_member_required
def field_definition_delete(request, field_id):
    """Delete a field definition - only accessible to staff members"""
    field = get_object_or_404(FieldDefinition, id=field_id)

    if request.method == 'POST':
        # Check if field is used in any guarantees
        if FieldValue.objects.filter(field=field).exists():
            messages.error(request, _('لا يمكن حذف الحقل لأنه مستخدم في كفالات موجودة'))
            return redirect('consultations:field_definition_list')

        # Log the action before deletion
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            model_name='FieldDefinition',
            object_id=field.id,
            description=_('تم حذف تعريف الحقل'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        field.delete()
        messages.success(request, _('تم حذف تعريف الحقل بنجاح'))
        return redirect('consultations:field_definition_list')

    context = {
        'field': field,
    }

    return render(request, 'consultations/dynamic/field_definition_confirm_delete.html', context)

@login_required
def dynamic_guarantee_list(request):
    """List all dynamic guarantees"""
    # Get filter parameters
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', 'all')

    # Base queryset
    guarantees = DynamicGuarantee.objects.all()

    # Apply filters
    if search_query:
        # Get field definitions for guarantor and guaranteed names
        try:
            guarantor_field = FieldDefinition.objects.get(field_key='guarantor_name')
            guaranteed_field = FieldDefinition.objects.get(field_key='guaranteed_name')
            bank_field = FieldDefinition.objects.get(field_key='bank_name')

            # Get guarantees with matching field values
            guarantor_values = FieldValue.objects.filter(field=guarantor_field, value__icontains=search_query).values_list('guarantee_id', flat=True)
            guaranteed_values = FieldValue.objects.filter(field=guaranteed_field, value__icontains=search_query).values_list('guarantee_id', flat=True)
            bank_values = FieldValue.objects.filter(field=bank_field, value__icontains=search_query).values_list('guarantee_id', flat=True)

            # Combine the results
            guarantees = guarantees.filter(Q(id__in=guarantor_values) | Q(id__in=guaranteed_values) | Q(id__in=bank_values))
        except FieldDefinition.DoesNotExist:
            # If field definitions don't exist, return empty queryset
            guarantees = DynamicGuarantee.objects.none()

    if status_filter == 'active':
        guarantees = guarantees.filter(is_active=True)
    elif status_filter == 'inactive':
        guarantees = guarantees.filter(is_active=False)

    # Order by
    guarantees = guarantees.order_by('-created_at')

    # Pagination
    paginator = Paginator(guarantees, 10)  # Show 10 guarantees per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
    }

    return render(request, 'consultations/dynamic/guarantee_list.html', context)

@login_required
def dynamic_guarantee_create(request):
    """Create a new dynamic guarantee"""
    # Get all active field definitions
    fields = FieldDefinition.objects.filter(is_active=True).order_by('section', 'display_order', 'name')

    if request.method == 'POST':
        # Create guarantee
        guarantee = DynamicGuarantee.objects.create(
            is_active=True,
            created_by=request.user,
            updated_by=request.user,
        )

        # Process field values
        for field in fields:
            field_key = f'field_{field.field_key}'

            if field.field_type == 'boolean':
                # For boolean fields, we only get a value if the checkbox is checked
                if field_key in request.POST:
                    value = 'true'
                else:
                    value = 'false'

                FieldValue.objects.create(
                    guarantee=guarantee,
                    field=field,
                    value=value
                )

            elif field.field_type == 'multi_select':
                # For multi-select fields, we get a list of values
                values = request.POST.getlist(field_key)
                if values:
                    # Store as comma-separated string
                    FieldValue.objects.create(
                        guarantee=guarantee,
                        field=field,
                        value=','.join(values)
                    )

            else:
                # For all other fields
                field_value = request.POST.get(field_key)
                if field_value:
                    FieldValue.objects.create(
                        guarantee=guarantee,
                        field=field,
                        value=field_value
                    )

        # Handle guarantee letter document
        if 'guarantee_letter' in request.FILES:
            DynamicGuaranteeDocument.objects.create(
                guarantee=guarantee,
                document_type='guarantee_letter',
                document_file=request.FILES['guarantee_letter'],
                description=_('كتاب الكفالة'),
                uploaded_by=request.user
            )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='DynamicGuarantee',
            object_id=guarantee.id,
            description=_('تم إنشاء كفالة جديدة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إنشاء الكفالة بنجاح'))
        return redirect('consultations:dynamic_guarantee_detail', guarantee_id=guarantee.id)

    # Group fields by section
    basic_fields = fields.filter(section='basic')
    additional_fields = fields.filter(section='additional')

    context = {
        'basic_fields': basic_fields,
        'additional_fields': additional_fields,
        'is_edit': False,
    }

    return render(request, 'consultations/dynamic/guarantee_form.html', context)

@login_required
def dynamic_guarantee_detail(request, guarantee_id):
    """Display dynamic guarantee details"""
    guarantee = get_object_or_404(DynamicGuarantee, id=guarantee_id)
    documents = guarantee.documents.all()
    field_values = guarantee.field_values.all().select_related('field')

    # Group field values by section
    basic_field_values = [fv for fv in field_values if fv.field.section == 'basic']
    additional_field_values = [fv for fv in field_values if fv.field.section == 'additional']

    context = {
        'guarantee': guarantee,
        'documents': documents,
        'basic_field_values': basic_field_values,
        'additional_field_values': additional_field_values,
        'today': timezone.now().date(),
    }

    return render(request, 'consultations/dynamic/guarantee_detail.html', context)

@login_required
def dynamic_guarantee_edit(request, guarantee_id):
    """Edit an existing dynamic guarantee"""
    guarantee = get_object_or_404(DynamicGuarantee, id=guarantee_id)
    fields = FieldDefinition.objects.filter(is_active=True).order_by('section', 'display_order', 'name')
    field_values = {fv.field.field_key: fv.value for fv in guarantee.field_values.all().select_related('field')}

    if request.method == 'POST':
        # Update guarantee
        guarantee.is_active = 'is_active' in request.POST
        guarantee.updated_by = request.user
        guarantee.save()

        # Update field values
        for field in fields:
            field_key = f'field_{field.field_key}'

            if field.field_type == 'boolean':
                # For boolean fields, we only get a value if the checkbox is checked
                if field_key in request.POST:
                    value = 'true'
                else:
                    value = 'false'

                FieldValue.objects.update_or_create(
                    guarantee=guarantee,
                    field=field,
                    defaults={'value': value}
                )

            elif field.field_type == 'multi_select':
                # For multi-select fields, we get a list of values
                values = request.POST.getlist(field_key)
                if values:
                    # Store as comma-separated string
                    FieldValue.objects.update_or_create(
                        guarantee=guarantee,
                        field=field,
                        defaults={'value': ','.join(values)}
                    )

            else:
                # For all other fields
                field_value = request.POST.get(field_key)
                if field_value:
                    FieldValue.objects.update_or_create(
                        guarantee=guarantee,
                        field=field,
                        defaults={'value': field_value}
                    )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='DynamicGuarantee',
            object_id=guarantee.id,
            description=_('تم تحديث الكفالة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث الكفالة بنجاح'))
        return redirect('consultations:dynamic_guarantee_detail', guarantee_id=guarantee.id)

    # Group fields by section
    basic_fields = fields.filter(section='basic')
    additional_fields = fields.filter(section='additional')

    context = {
        'guarantee': guarantee,
        'basic_fields': basic_fields,
        'additional_fields': additional_fields,
        'field_values': field_values,
        'is_edit': True,
    }

    return render(request, 'consultations/dynamic/guarantee_form.html', context)

@login_required
def dynamic_guarantee_delete(request, guarantee_id):
    """Delete a dynamic guarantee"""
    guarantee = get_object_or_404(DynamicGuarantee, id=guarantee_id)

    if request.method == 'POST':
        # Log the action before deletion
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            model_name='DynamicGuarantee',
            object_id=guarantee.id,
            description=_('تم حذف الكفالة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        guarantee.delete()
        messages.success(request, _('تم حذف الكفالة بنجاح'))
        return redirect('consultations:dynamic_guarantee_list')

    context = {
        'guarantee': guarantee,
    }

    return render(request, 'consultations/dynamic/guarantee_confirm_delete.html', context)

@login_required
def upload_dynamic_guarantee_document(request, guarantee_id):
    """Upload a document for a dynamic guarantee"""
    guarantee = get_object_or_404(DynamicGuarantee, id=guarantee_id)

    if request.method == 'POST':
        document_type = request.POST.get('document_type')
        description = request.POST.get('description')
        document_file = request.FILES.get('document_file')

        if not document_file:
            messages.error(request, _('يرجى اختيار ملف'))
            return redirect('consultations:upload_dynamic_guarantee_document', guarantee_id=guarantee.id)

        DynamicGuaranteeDocument.objects.create(
            guarantee=guarantee,
            document_type=document_type,
            description=description,
            document_file=document_file,
            uploaded_by=request.user
        )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='DynamicGuaranteeDocument',
            object_id=guarantee.id,
            description=_('تم رفع مستند للكفالة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم رفع المستند بنجاح'))
        return redirect('consultations:dynamic_guarantee_detail', guarantee_id=guarantee.id)

    context = {
        'guarantee': guarantee,
    }

    return render(request, 'consultations/dynamic/upload_document.html', context)

@staff_member_required
def initialize_field_definitions(request):
    """Initialize default field definitions - only accessible to staff members"""
    if not request.user.is_superuser:
        messages.error(request, _('غير مسموح'))
        return redirect('consultations:field_definition_list')

    # Check if field definitions already exist
    if FieldDefinition.objects.exists():
        messages.warning(request, _('تم تهيئة تعريفات الحقول مسبقاً'))
        return redirect('consultations:field_definition_list')

    # Define default fields
    default_fields = [
        # Basic information fields
        {
            'name': 'اسم الكفيل',
            'field_key': 'guarantor_name',
            'field_type': 'text',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 1,
        },
        {
            'name': 'اسم المكفول',
            'field_key': 'guaranteed_name',
            'field_type': 'text',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 2,
        },
        {
            'name': 'مدة الكفالة',
            'field_key': 'guarantee_duration',
            'field_type': 'text',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 3,
        },
        {
            'name': 'مبلغ الكفالة',
            'field_key': 'guarantee_amount',
            'field_type': 'number',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 4,
        },

        # Additional information fields
        {
            'name': 'اسم المصرف المانح',
            'field_key': 'bank_name',
            'field_type': 'text',
            'section': 'additional',
            'is_required': True,
            'is_system': True,
            'display_order': 1,
        },
        {
            'name': 'تاريخ منح الكفالة',
            'field_key': 'issue_date',
            'field_type': 'date',
            'section': 'additional',
            'is_required': True,
            'is_system': True,
            'display_order': 2,
        },
        {
            'name': 'تاريخ انتهاء الكفالة',
            'field_key': 'expiry_date',
            'field_type': 'date',
            'section': 'additional',
            'is_required': True,
            'is_system': True,
            'display_order': 3,
        },
    ]

    # Create field definitions
    for field_data in default_fields:
        FieldDefinition.objects.create(
            name=field_data['name'],
            field_key=field_data['field_key'],
            field_type=field_data['field_type'],
            section=field_data['section'],
            is_required=field_data['is_required'],
            is_system=field_data['is_system'],
            display_order=field_data['display_order'],
            is_active=True,
            created_by=request.user,
            updated_by=request.user,
        )

    # Log the action
    AuditLog.objects.create(
        user=request.user,
        action='create',
        model_name='FieldDefinition',
        object_id=0,
        description=_('تم تهيئة تعريفات الحقول الافتراضية'),
        ip_address=request.META.get('REMOTE_ADDR')
    )

    messages.success(request, _('تم تهيئة تعريفات الحقول الافتراضية بنجاح'))
    return redirect('consultations:field_definition_list')
