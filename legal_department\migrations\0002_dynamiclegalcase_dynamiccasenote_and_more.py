# Generated by Django 5.2 on 2025-04-16 20:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
        ('legal_department', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DynamicLegalCase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('open', 'مفتوحة'), ('closed', 'مغلقة'), ('pending', 'قيد الانتظار')], default='open', max_length=20, verbose_name='حالة القضية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_dynamic_cases', to=settings.AUTH_USER_MODEL, verbose_name='مسند إلى')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_dynamic_cases', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('department', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dynamic_cases', to='core.department', verbose_name='القسم')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_dynamic_cases', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'دعوى ديناميكية',
                'verbose_name_plural': 'دعاوى ديناميكية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DynamicCaseNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.TextField(verbose_name='الملاحظة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dynamic_case_notes', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='legal_department.dynamiclegalcase', verbose_name='الدعوى')),
            ],
            options={
                'verbose_name': 'ملاحظة الدعوى الديناميكية',
                'verbose_name_plural': 'ملاحظات الدعاوى الديناميكية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DynamicLegalDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='عنوان المستند')),
                ('document_file', models.FileField(upload_to='dynamic_legal_documents/', verbose_name='الملف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='legal_department.dynamiclegalcase', verbose_name='الدعوى')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_dynamic_legal_documents', to=settings.AUTH_USER_MODEL, verbose_name='تم الرفع بواسطة')),
            ],
            options={
                'verbose_name': 'مستند الدعوى الديناميكي',
                'verbose_name_plural': 'مستندات الدعاوى الديناميكية',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='FieldDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='اسم الحقل')),
                ('field_key', models.CharField(max_length=100, unique=True, verbose_name='مفتاح الحقل')),
                ('field_type', models.CharField(choices=[('text', 'نص'), ('number', 'رقم'), ('date', 'تاريخ'), ('boolean', 'نعم/لا'), ('select', 'قائمة منسدلة'), ('multi_select', 'اختيار متعدد')], max_length=20, verbose_name='نوع الحقل')),
                ('section', models.CharField(choices=[('basic', 'المعلومات الأساسية'), ('additional', 'معلومات إضافية')], default='basic', max_length=20, verbose_name='القسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الحقل')),
                ('options', models.TextField(blank=True, null=True, verbose_name='خيارات الحقل (للقوائم المنسدلة)')),
                ('is_required', models.BooleanField(default=False, verbose_name='مطلوب')),
                ('is_system', models.BooleanField(default=False, verbose_name='حقل نظام')),
                ('is_active', models.BooleanField(default=True, verbose_name='فعال')),
                ('display_order', models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_case_fields', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'تعريف حقل الدعوى',
                'verbose_name_plural': 'تعريفات حقول الدعاوى',
                'ordering': ['section', 'display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='FieldValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.TextField(verbose_name='القيمة')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='field_values', to='legal_department.dynamiclegalcase', verbose_name='الدعوى')),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='case_values', to='legal_department.fielddefinition', verbose_name='الحقل')),
            ],
            options={
                'verbose_name': 'قيمة حقل الدعوى',
                'verbose_name_plural': 'قيم حقول الدعاوى',
                'unique_together': {('case', 'field')},
            },
        ),
    ]
