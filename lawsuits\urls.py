from django.urls import path
from . import views
from . import dynamic_views

app_name = 'lawsuits'

urlpatterns = [
    path('', views.index, name='index'),
    path('councils/', views.council_list, name='council_list'),
    path('councils/create/', views.council_create, name='council_create'),
    path('councils/<int:council_id>/', views.council_detail, name='council_detail'),
    path('councils/<int:council_id>/edit/', views.council_edit, name='council_edit'),
    path('councils/<int:council_id>/delete/', views.council_delete, name='council_delete'),
    path('councils/<int:council_id>/close/', views.council_close, name='council_close'),
    path('councils/<int:council_id>/extend/', views.council_extend, name='council_extend'),
    path('councils/<int:council_id>/members/', views.council_members, name='council_members'),
    path('councils/<int:council_id>/members/add/', views.add_council_member, name='add_council_member'),
    path('councils/<int:council_id>/members/<int:member_id>/edit/', views.edit_council_member, name='edit_council_member'),
    path('councils/<int:council_id>/members/<int:member_id>/delete/', views.delete_council_member, name='delete_council_member'),
    path('councils/<int:council_id>/upload-document/', views.upload_council_document, name='upload_council_document'),

    # Dynamic council URLs
    path('dynamic/', dynamic_views.dynamic_index, name='dynamic_index'),
    path('dynamic/fields/', dynamic_views.field_definition_list, name='field_definition_list'),
    path('dynamic/fields/create/', dynamic_views.field_definition_create, name='field_definition_create'),
    path('dynamic/fields/<int:field_id>/edit/', dynamic_views.field_definition_edit, name='field_definition_edit'),
    path('dynamic/fields/<int:field_id>/delete/', dynamic_views.field_definition_delete, name='field_definition_delete'),
    path('dynamic/fields/initialize/', dynamic_views.initialize_council_fields, name='initialize_council_fields'),
    path('dynamic/councils/', dynamic_views.dynamic_council_list, name='dynamic_council_list'),
    path('dynamic/councils/create/', dynamic_views.dynamic_council_create, name='dynamic_council_create'),
    path('dynamic/councils/<int:council_id>/', dynamic_views.dynamic_council_detail, name='dynamic_council_detail'),
    path('dynamic/councils/<int:council_id>/edit/', dynamic_views.dynamic_council_edit, name='dynamic_council_edit'),
    path('dynamic/councils/<int:council_id>/delete/', dynamic_views.dynamic_council_delete, name='dynamic_council_delete'),
    path('dynamic/councils/<int:council_id>/members/', dynamic_views.dynamic_council_members, name='dynamic_council_members'),
    path('dynamic/councils/<int:council_id>/members/add/', dynamic_views.add_dynamic_council_member, name='add_dynamic_council_member'),
    path('dynamic/councils/<int:council_id>/members/<int:member_id>/edit/', dynamic_views.edit_dynamic_council_member, name='edit_dynamic_council_member'),
    path('dynamic/councils/<int:council_id>/members/<int:member_id>/delete/', dynamic_views.delete_dynamic_council_member, name='delete_dynamic_council_member'),
    path('dynamic/councils/<int:council_id>/upload-document/', dynamic_views.upload_dynamic_council_document, name='upload_dynamic_council_document'),
]
