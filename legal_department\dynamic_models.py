from django.db import models
from django.contrib.auth.models import User
from core.models import Department

class FieldDefinition(models.Model):
    """Model for defining fields in the legal case system"""
    class Meta:
        app_label = 'legal_department'
    FIELD_TYPES = (
        ('text', 'نص'),
        ('number', 'رقم'),
        ('date', 'تاريخ'),
        ('boolean', 'نعم/لا'),
        ('select', 'قائمة منسدلة'),
        ('multi_select', 'اختيار متعدد'),
    )

    SECTIONS = (
        ('basic', 'المعلومات الأساسية'),
        ('additional', 'معلومات إضافية'),
    )

    name = models.CharField(max_length=255, verbose_name="اسم الحقل")
    field_key = models.CharField(max_length=100, unique=True, verbose_name="مفتاح الحقل")
    field_type = models.CharField(max_length=20, choices=FIELD_TYPES, verbose_name="نوع الحقل")
    section = models.CharField(max_length=20, choices=SECTIONS, default='basic', verbose_name="القسم")
    description = models.TextField(blank=True, null=True, verbose_name="وصف الحقل")
    options = models.TextField(blank=True, null=True, verbose_name="خيارات الحقل (للقوائم المنسدلة)")
    is_required = models.BooleanField(default=False, verbose_name="مطلوب")
    is_system = models.BooleanField(default=False, verbose_name="حقل نظام")
    is_active = models.BooleanField(default=True, verbose_name="فعال")
    display_order = models.PositiveIntegerField(default=0, verbose_name="ترتيب العرض")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_case_fields', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "تعريف حقل الدعوى"
        verbose_name_plural = "تعريفات حقول الدعاوى"
        ordering = ['section', 'display_order', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_field_type_display()})"

    def get_options_list(self):
        """Get options as a list"""
        if self.options and (self.field_type == 'select' or self.field_type == 'multi_select'):
            return [option.strip() for option in self.options.split(',')]
        return []

class DynamicLegalCase(models.Model):
    """Model for dynamic legal cases"""
    class Meta:
        app_label = 'legal_department'
    CASE_STATUS = (
        ('open', 'مفتوحة'),
        ('closed', 'مغلقة'),
        ('pending', 'قيد الانتظار'),
    )

    status = models.CharField(max_length=20, choices=CASE_STATUS, default='open', verbose_name="حالة القضية")
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, related_name='dynamic_cases', verbose_name="القسم")
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_dynamic_cases', verbose_name="مسند إلى")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_dynamic_cases', verbose_name="تم الإنشاء بواسطة")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='updated_dynamic_cases', verbose_name="تم التحديث بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "دعوى ديناميكية"
        verbose_name_plural = "دعاوى ديناميكية"
        ordering = ['-created_at']

    def __str__(self):
        # Try to get title and case number from field values
        title = self.get_field_value('title')
        case_number = self.get_field_value('case_number')

        if title:
            if case_number:
                return f"{title} - {case_number}"
            return title
        return f"دعوى #{self.id}"

    def get_field_value(self, field_key):
        """Get the value of a field by its key"""
        try:
            field_def = FieldDefinition.objects.get(field_key=field_key, is_active=True)
            field_value = self.field_values.get(field=field_def)
            return field_value.value
        except (FieldDefinition.DoesNotExist, FieldValue.DoesNotExist):
            return None

class FieldValue(models.Model):
    """Model for field values in legal cases"""
    class Meta:
        app_label = 'legal_department'
    case = models.ForeignKey(DynamicLegalCase, on_delete=models.CASCADE, related_name='field_values', verbose_name="الدعوى")
    field = models.ForeignKey(FieldDefinition, on_delete=models.CASCADE, related_name='case_values', verbose_name="الحقل")
    value = models.TextField(verbose_name="القيمة")

    class Meta:
        verbose_name = "قيمة حقل الدعوى"
        verbose_name_plural = "قيم حقول الدعاوى"
        unique_together = ('case', 'field')

    def __str__(self):
        return f"{self.field.name}: {self.value}"

class DynamicLegalDocument(models.Model):
    """Model for dynamic legal documents"""
    class Meta:
        app_label = 'legal_department'
    case = models.ForeignKey(DynamicLegalCase, on_delete=models.CASCADE, related_name='documents', verbose_name="الدعوى")
    title = models.CharField(max_length=255, verbose_name="عنوان المستند")
    document_file = models.FileField(upload_to='dynamic_legal_documents/', verbose_name="الملف")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='uploaded_dynamic_legal_documents', verbose_name="تم الرفع بواسطة")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الرفع")

    class Meta:
        verbose_name = "مستند الدعوى الديناميكي"
        verbose_name_plural = "مستندات الدعاوى الديناميكية"
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.title} - {self.case}"

class DynamicCaseNote(models.Model):
    """Model for dynamic case notes"""
    class Meta:
        app_label = 'legal_department'
    case = models.ForeignKey(DynamicLegalCase, on_delete=models.CASCADE, related_name='notes', verbose_name="الدعوى")
    note = models.TextField(verbose_name="الملاحظة")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='dynamic_case_notes', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "ملاحظة الدعوى الديناميكية"
        verbose_name_plural = "ملاحظات الدعاوى الديناميكية"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.case} - {self.created_at}"
