{% extends 'base/base.html' %}

{% block title %}قسم الدعاوى والمجالس - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}قسم الدعاوى والمجالس{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">إجمالي المجالس/اللجان</h6>
                            <h2 class="mb-0">{{ council_count }}</h2>
                        </div>
                        <i class="fas fa-gavel fa-3x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <span>عرض التفاصيل</span>
                    <a href="{% url 'lawsuits:council_list' %}" class="text-white">
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">المجالس/اللجان الفعالة</h6>
                            <h2 class="mb-0">{{ active_council_count }}</h2>
                        </div>
                        <i class="fas fa-clipboard-check fa-3x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <span>عرض التفاصيل</span>
                    <a href="{% url 'lawsuits:council_list' %}?status=active" class="text-white">
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">المجالس/اللجان المتجاوزة للمدة</h6>
                            <h2 class="mb-0">{{ expired_council_count }}</h2>
                        </div>
                        <i class="fas fa-exclamation-triangle fa-3x"></i>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between">
                    <span>عرض التفاصيل</span>
                    <a href="{% url 'lawsuits:council_list' %}?status=expired" class="text-white">
                        <i class="fas fa-arrow-circle-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">إجراءات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">

                        <div class="col-md-3 mb-3">
                            <a href="{% url 'lawsuits:council_list' %}" class="btn btn-info w-100">
                                <i class="fas fa-list me-2"></i> عرض جميع المجالس/اللجان
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'dashboard:reports' %}" class="btn btn-warning w-100">
                                <i class="fas fa-chart-bar me-2"></i> تقارير المجالس/اللجان
                            </a>
                        </div>
                        {% if user.is_staff %}
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'lawsuits:field_definition_list' %}" class="btn btn-secondary w-100">
                                <i class="fas fa-cogs me-2"></i> إدارة تعريفات الحقول
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{% url 'lawsuits:dynamic_council_create' %}" class="btn btn-primary w-100">
                                <i class="fas fa-plus-circle me-2"></i> إضافة مجلس/لجنة جديدة
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Councils -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث المجالس/اللجان</h5>
                    <a href="{% url 'lawsuits:council_list' %}" class="text-white">
                        <i class="fas fa-external-link-alt"></i> عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>نوع المجلس/اللجنة</th>
                                    <th>أمر التشكيل</th>
                                    <th>تاريخ التشكيل</th>
                                    <th>المنفذ/الدائرة</th>
                                    <th>المدة (أيام)</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for council in recent_councils %}
                                <tr>
                                    <td>{{ council.get_council_type_display }}</td>
                                    <td>{{ council.formation_order }}</td>
                                    <td>{{ council.formation_date }}</td>
                                    <td>{{ council.port_name }}</td>
                                    <td>{{ council.duration }}</td>
                                    <td>
                                        {% if council.is_closed %}
                                            <span class="badge bg-secondary">مغلق</span>
                                        {% else %}
                                            {% if council.is_expired %}
                                                <span class="badge bg-danger">متجاوز للمدة</span>
                                            {% else %}
                                                <span class="badge bg-success">فعال</span>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'lawsuits:council_detail' council.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'lawsuits:council_edit' council.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد مجالس/لجان</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
