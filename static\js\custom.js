// Custom JavaScript for the Legal Department Management System

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl)
    });
    
    // Font size adjustment
    const fontSizeControls = document.querySelectorAll('.font-size-control');
    if (fontSizeControls.length > 0) {
        fontSizeControls.forEach(control => {
            control.addEventListener('click', function(e) {
                e.preventDefault();
                const action = this.dataset.action;
                const currentSize = parseInt(getComputedStyle(document.body).fontSize);
                
                if (action === 'increase') {
                    document.body.style.fontSize = (currentSize + 1) + 'px';
                } else if (action === 'decrease') {
                    document.body.style.fontSize = (currentSize - 1) + 'px';
                } else if (action === 'reset') {
                    document.body.style.fontSize = '16px';
                }
                
                // Save preference to localStorage
                localStorage.setItem('fontSize', document.body.style.fontSize);
            });
        });
        
        // Apply saved font size preference
        const savedFontSize = localStorage.getItem('fontSize');
        if (savedFontSize) {
            document.body.style.fontSize = savedFontSize;
        }
    }
    
    // Dynamic form fields
    const addFieldButton = document.getElementById('add-field-button');
    if (addFieldButton) {
        addFieldButton.addEventListener('click', function() {
            const fieldsContainer = document.getElementById('dynamic-fields');
            const fieldCount = fieldsContainer.children.length;
            
            const fieldTemplate = `
                <div class="row mb-3 dynamic-field">
                    <div class="col-md-5">
                        <input type="text" class="form-control" name="field_name_${fieldCount}" placeholder="اسم الحقل" required>
                    </div>
                    <div class="col-md-5">
                        <select class="form-select" name="field_type_${fieldCount}" required>
                            <option value="" selected disabled>نوع الحقل</option>
                            <option value="text">نص</option>
                            <option value="number">رقم</option>
                            <option value="date">تاريخ</option>
                            <option value="boolean">نعم/لا</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-danger remove-field-button">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            
            fieldsContainer.insertAdjacentHTML('beforeend', fieldTemplate);
            
            // Update the field count
            document.getElementById('field_count').value = fieldCount + 1;
        });
        
        // Remove dynamic field
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-field-button') || e.target.closest('.remove-field-button')) {
                const fieldRow = e.target.closest('.dynamic-field');
                fieldRow.remove();
                
                // Update the field count
                const fieldsContainer = document.getElementById('dynamic-fields');
                document.getElementById('field_count').value = fieldsContainer.children.length;
            }
        });
    }
    
    // Council members management
    const addMemberButton = document.getElementById('add-member-button');
    if (addMemberButton) {
        addMemberButton.addEventListener('click', function() {
            const membersContainer = document.getElementById('council-members');
            const memberCount = membersContainer.children.length;
            
            const memberTemplate = `
                <div class="row mb-3 council-member">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="member_name_${memberCount}" placeholder="اسم العضو" required>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="member_position_${memberCount}" placeholder="مقر العمل" required>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input member-head-checkbox" type="checkbox" name="member_is_head_${memberCount}" id="member_is_head_${memberCount}">
                            <label class="form-check-label" for="member_is_head_${memberCount}">
                                رئيس اللجنة
                            </label>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <button type="button" class="btn btn-danger remove-member-button">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            
            membersContainer.insertAdjacentHTML('beforeend', memberTemplate);
            
            // Update the member count
            document.getElementById('member_count').value = memberCount + 1;
        });
        
        // Remove council member
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-member-button') || e.target.closest('.remove-member-button')) {
                const memberRow = e.target.closest('.council-member');
                memberRow.remove();
                
                // Update the member count
                const membersContainer = document.getElementById('council-members');
                document.getElementById('member_count').value = membersContainer.children.length;
            }
        });
        
        // Ensure only one member is head
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('member-head-checkbox') && e.target.checked) {
                const checkboxes = document.querySelectorAll('.member-head-checkbox');
                checkboxes.forEach(checkbox => {
                    if (checkbox !== e.target) {
                        checkbox.checked = false;
                    }
                });
            }
        });
    }
    
    // Date range filter
    const dateRangeFilter = document.getElementById('date-range-filter');
    if (dateRangeFilter) {
        dateRangeFilter.addEventListener('change', function() {
            const value = this.value;
            const startDateInput = document.getElementById('start_date');
            const endDateInput = document.getElementById('end_date');
            
            const today = new Date();
            let startDate = new Date();
            
            if (value === 'today') {
                startDate = today;
            } else if (value === 'yesterday') {
                startDate.setDate(today.getDate() - 1);
            } else if (value === 'last_7_days') {
                startDate.setDate(today.getDate() - 7);
            } else if (value === 'last_30_days') {
                startDate.setDate(today.getDate() - 30);
            } else if (value === 'this_month') {
                startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            } else if (value === 'last_month') {
                startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const endDate = new Date(today.getFullYear(), today.getMonth(), 0);
                endDateInput.value = formatDate(endDate);
            } else if (value === 'custom') {
                startDateInput.value = '';
                endDateInput.value = '';
                startDateInput.disabled = false;
                endDateInput.disabled = false;
                return;
            }
            
            startDateInput.value = formatDate(startDate);
            if (value !== 'last_month') {
                endDateInput.value = formatDate(today);
            }
            
            startDateInput.disabled = value !== 'custom';
            endDateInput.disabled = value !== 'custom';
        });
    }
    
    // Helper function to format date as YYYY-MM-DD
    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    
    // File upload preview
    const fileInputs = document.querySelectorAll('.custom-file-input');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const fileName = this.files[0].name;
            const label = this.nextElementSibling;
            label.textContent = fileName;
        });
    });
    
    // Confirm delete actions
    const deleteButtons = document.querySelectorAll('.delete-confirm');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                e.preventDefault();
            }
        });
    });
});
