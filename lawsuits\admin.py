from django.contrib import admin
from .models import Council, CouncilMember, CouncilDocument

# Import dynamic models
from .dynamic_models import <PERSON>ynamicCouncil, DynamicCouncilMember, DynamicCouncilDocument
from .dynamic_admin import DynamicCouncilAdmin, DynamicCouncilMemberAdmin, DynamicCouncilDocumentAdmin

# Register dynamic models
admin.site.register(DynamicCouncil, DynamicCouncilAdmin)
admin.site.register(DynamicCouncilMember, DynamicCouncilMemberAdmin)
admin.site.register(DynamicCouncilDocument, DynamicCouncilDocumentAdmin)

class CouncilMemberInline(admin.TabularInline):
    model = CouncilMember
    extra = 1

class CouncilDocumentInline(admin.TabularInline):
    model = CouncilDocument
    extra = 1

@admin.register(Council)
class CouncilAdmin(admin.ModelAdmin):
    list_display = ('get_council_type_display', 'formation_order', 'formation_date', 'port_name', 'duration', 'is_closed', 'is_expired')
    list_filter = ('council_type', 'is_closed', 'formation_date', 'extended')
    search_fields = ('formation_order', 'port_name', 'subject', 'last_action')
    date_hierarchy = 'formation_date'
    readonly_fields = ('created_by', 'updated_by', 'created_at', 'updated_at')
    inlines = [CouncilMemberInline, CouncilDocumentInline]

    def is_expired(self, obj):
        return obj.is_expired()
    is_expired.boolean = True
    is_expired.short_description = 'متجاوز للمدة'

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(CouncilMember)
class CouncilMemberAdmin(admin.ModelAdmin):
    list_display = ('name', 'position', 'council', 'is_head')
    list_filter = ('is_head',)
    search_fields = ('name', 'position', 'council__formation_order')

@admin.register(CouncilDocument)
class CouncilDocumentAdmin(admin.ModelAdmin):
    list_display = ('council', 'document_type', 'uploaded_by', 'uploaded_at')
    list_filter = ('document_type', 'uploaded_at')
    search_fields = ('council__formation_order', 'description')
    readonly_fields = ('uploaded_by', 'uploaded_at')

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)
