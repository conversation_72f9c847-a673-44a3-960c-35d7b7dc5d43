{% extends 'base/base.html' %}

{% block title %}قائمة الكفالات - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}قائمة الكفالات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">فلاتر البحث</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">بحث</label>
                    <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="اسم الكفيل، المكفول، المصرف...">
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {% if status_filter == 'all' %}selected{% endif %}>الكل</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>فعالة</option>
                        <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير فعالة</option>
                        <option value="expired" {% if status_filter == 'expired' %}selected{% endif %}>منتهية</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="bank" class="form-label">المصرف</label>
                    <select class="form-select" id="bank" name="bank">
                        <option value="">الكل</option>
                        {% for bank in bank_names %}
                        <option value="{{ bank }}" {% if bank_filter == bank %}selected{% endif %}>{{ bank }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                </div>
                
                <div class="col-md-2">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                </div>
                
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Guarantees Table -->
    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">الكفالات</h5>
            <a href="{% url 'consultations:guarantee_create' %}" class="btn btn-light btn-sm">
                <i class="fas fa-plus-circle"></i> إضافة كفالة جديدة
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <a href="?{% if 'guarantor_name' in order_by %}-{% endif %}guarantor_name=&search={{ search_query }}&status={{ status_filter }}&bank={{ bank_filter }}&start_date={{ start_date }}&end_date={{ end_date }}">
                                    اسم الكفيل
                                    {% if order_by == 'guarantor_name' %}
                                    <i class="fas fa-sort-up"></i>
                                    {% elif order_by == '-guarantor_name' %}
                                    <i class="fas fa-sort-down"></i>
                                    {% else %}
                                    <i class="fas fa-sort"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?{% if 'guaranteed_name' in order_by %}-{% endif %}guaranteed_name=&search={{ search_query }}&status={{ status_filter }}&bank={{ bank_filter }}&start_date={{ start_date }}&end_date={{ end_date }}">
                                    اسم المكفول
                                    {% if order_by == 'guaranteed_name' %}
                                    <i class="fas fa-sort-up"></i>
                                    {% elif order_by == '-guaranteed_name' %}
                                    <i class="fas fa-sort-down"></i>
                                    {% else %}
                                    <i class="fas fa-sort"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?{% if 'guarantee_amount' in order_by %}-{% endif %}guarantee_amount=&search={{ search_query }}&status={{ status_filter }}&bank={{ bank_filter }}&start_date={{ start_date }}&end_date={{ end_date }}">
                                    مبلغ الكفالة
                                    {% if order_by == 'guarantee_amount' %}
                                    <i class="fas fa-sort-up"></i>
                                    {% elif order_by == '-guarantee_amount' %}
                                    <i class="fas fa-sort-down"></i>
                                    {% else %}
                                    <i class="fas fa-sort"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?{% if 'bank_name' in order_by %}-{% endif %}bank_name=&search={{ search_query }}&status={{ status_filter }}&bank={{ bank_filter }}&start_date={{ start_date }}&end_date={{ end_date }}">
                                    المصرف المانح
                                    {% if order_by == 'bank_name' %}
                                    <i class="fas fa-sort-up"></i>
                                    {% elif order_by == '-bank_name' %}
                                    <i class="fas fa-sort-down"></i>
                                    {% else %}
                                    <i class="fas fa-sort"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?{% if 'issue_date' in order_by %}-{% endif %}issue_date=&search={{ search_query }}&status={{ status_filter }}&bank={{ bank_filter }}&start_date={{ start_date }}&end_date={{ end_date }}">
                                    تاريخ الإصدار
                                    {% if order_by == 'issue_date' %}
                                    <i class="fas fa-sort-up"></i>
                                    {% elif order_by == '-issue_date' %}
                                    <i class="fas fa-sort-down"></i>
                                    {% else %}
                                    <i class="fas fa-sort"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?{% if 'expiry_date' in order_by %}-{% endif %}expiry_date=&search={{ search_query }}&status={{ status_filter }}&bank={{ bank_filter }}&start_date={{ start_date }}&end_date={{ end_date }}">
                                    تاريخ الانتهاء
                                    {% if order_by == 'expiry_date' %}
                                    <i class="fas fa-sort-up"></i>
                                    {% elif order_by == '-expiry_date' %}
                                    <i class="fas fa-sort-down"></i>
                                    {% else %}
                                    <i class="fas fa-sort"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for guarantee in page_obj %}
                        <tr>
                            <td>{{ guarantee.guarantor_name }}</td>
                            <td>{{ guarantee.guaranteed_name }}</td>
                            <td>{{ guarantee.guarantee_amount }}</td>
                            <td>{{ guarantee.bank_name }}</td>
                            <td>{{ guarantee.issue_date }}</td>
                            <td>{{ guarantee.expiry_date }}</td>
                            <td>
                                {% if guarantee.is_active %}
                                    {% if guarantee.expiry_date < today %}
                                        <span class="badge bg-danger">منتهية</span>
                                    {% else %}
                                        <span class="badge bg-success">فعالة</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-secondary">غير فعالة</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'consultations:guarantee_detail' guarantee.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'consultations:guarantee_edit' guarantee.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'consultations:guarantee_delete' guarantee.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">لا توجد كفالات مطابقة لمعايير البحث</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-4">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1&search={{ search_query }}&status={{ status_filter }}&bank={{ bank_filter }}&start_date={{ start_date }}&end_date={{ end_date }}&order_by={{ order_by }}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}&search={{ search_query }}&status={{ status_filter }}&bank={{ bank_filter }}&start_date={{ start_date }}&end_date={{ end_date }}&order_by={{ order_by }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                        <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                        {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ i }}&search={{ search_query }}&status={{ status_filter }}&bank={{ bank_filter }}&start_date={{ start_date }}&end_date={{ end_date }}&order_by={{ order_by }}">{{ i }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}&search={{ search_query }}&status={{ status_filter }}&bank={{ bank_filter }}&start_date={{ start_date }}&end_date={{ end_date }}&order_by={{ order_by }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}&search={{ search_query }}&status={{ status_filter }}&bank={{ bank_filter }}&start_date={{ start_date }}&end_date={{ end_date }}&order_by={{ order_by }}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
