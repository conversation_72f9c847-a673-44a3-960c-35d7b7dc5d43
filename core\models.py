from django.db import models
from django.contrib.auth.models import User

class Department(models.Model):
    """Model for departments in the legal department system"""
    name = models.CharField(max_length=100, verbose_name="اسم القسم")
    description = models.TextField(blank=True, null=True, verbose_name="وصف القسم")
    is_active = models.BooleanField(default=True, verbose_name="فعال")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "قسم"
        verbose_name_plural = "الأقسام"
        ordering = ['name']

    def __str__(self):
        return self.name

class UserProfile(models.Model):
    """Extended user profile for the system"""
    USER_TYPES = (
        ('admin', 'مدير النظام'),
        ('department_head', 'رئيس قسم'),
        ('data_entry', 'مدخل بيانات'),
        ('general_manager', 'المدير العام'),
    )

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile', verbose_name="المستخدم")
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True, related_name='users', verbose_name="القسم")
    user_type = models.CharField(max_length=20, choices=USER_TYPES, default='data_entry', verbose_name="نوع المستخدم")
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    profile_picture = models.ImageField(upload_to='profile_pictures/', blank=True, null=True, verbose_name="الصورة الشخصية")

    class Meta:
        verbose_name = "ملف المستخدم"
        verbose_name_plural = "ملفات المستخدمين"

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.get_user_type_display()}"

class SystemSetting(models.Model):
    """System settings for the application"""
    key = models.CharField(max_length=50, unique=True, verbose_name="المفتاح")
    value = models.TextField(verbose_name="القيمة")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")

    class Meta:
        verbose_name = "إعداد النظام"
        verbose_name_plural = "إعدادات النظام"

    def __str__(self):
        return self.key

class AuditLog(models.Model):
    """Audit log for tracking user actions"""
    ACTION_TYPES = (
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('backup', 'نسخ احتياطي'),
        ('restore', 'استعادة'),
    )

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='audit_logs', verbose_name="المستخدم")
    action = models.CharField(max_length=20, choices=ACTION_TYPES, verbose_name="الإجراء")
    model_name = models.CharField(max_length=100, blank=True, null=True, verbose_name="اسم النموذج")
    object_id = models.PositiveIntegerField(blank=True, null=True, verbose_name="معرف الكائن")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    ip_address = models.GenericIPAddressField(blank=True, null=True, verbose_name="عنوان IP")
    timestamp = models.DateTimeField(auto_now_add=True, verbose_name="التاريخ والوقت")

    class Meta:
        verbose_name = "سجل التدقيق"
        verbose_name_plural = "سجلات التدقيق"
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.user} - {self.get_action_display()} - {self.timestamp}"
