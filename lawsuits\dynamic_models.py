from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

class FieldDefinition(models.Model):
    """Model for defining fields in the council system"""
    class Meta:
        app_label = 'lawsuits'
    FIELD_TYPES = (
        ('text', 'نص'),
        ('number', 'رقم'),
        ('date', 'تاريخ'),
        ('boolean', 'نعم/لا'),
        ('select', 'قائمة منسدلة'),
        ('multi_select', 'اختيار متعدد'),
    )

    SECTIONS = (
        ('basic', 'المعلومات الأساسية'),
        ('additional', 'معلومات إضافية'),
    )

    name = models.CharField(max_length=255, verbose_name="اسم الحقل")
    field_key = models.CharField(max_length=100, unique=True, verbose_name="مفتاح الحقل")
    field_type = models.CharField(max_length=20, choices=FIELD_TYPES, verbose_name="نوع الحقل")
    section = models.CharField(max_length=20, choices=SECTIONS, default='basic', verbose_name="القسم")
    description = models.TextField(blank=True, null=True, verbose_name="وصف الحقل")
    options = models.TextField(blank=True, null=True, verbose_name="خيارات الحقل (للقوائم المنسدلة)")
    is_required = models.BooleanField(default=False, verbose_name="مطلوب")
    is_system = models.BooleanField(default=False, verbose_name="حقل نظام")
    is_active = models.BooleanField(default=True, verbose_name="فعال")
    display_order = models.PositiveIntegerField(default=0, verbose_name="ترتيب العرض")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_council_fields', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "تعريف حقل المجلس"
        verbose_name_plural = "تعريفات حقول المجالس"
        ordering = ['section', 'display_order', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_field_type_display()})"

    def get_options_list(self):
        """Get options as a list"""
        if self.options and (self.field_type == 'select' or self.field_type == 'multi_select'):
            return [option.strip() for option in self.options.split(',')]
        return []

class DynamicCouncil(models.Model):
    """Model for dynamic councils"""
    class Meta:
        app_label = 'lawsuits'
    is_closed = models.BooleanField(default=False, verbose_name="مغلق")
    extended = models.BooleanField(default=False, verbose_name="تم التمديد")
    extension_days = models.PositiveIntegerField(default=0, verbose_name="أيام التمديد")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_dynamic_councils', verbose_name="تم الإنشاء بواسطة")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='updated_dynamic_councils', verbose_name="تم التحديث بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مجلس ديناميكي"
        verbose_name_plural = "مجالس ديناميكية"
        ordering = ['-created_at']

    def __str__(self):
        # Try to get formation order and subject from field values
        formation_order = self.get_field_value('formation_order')
        subject = self.get_field_value('subject')

        if formation_order and subject:
            return f"{formation_order} - {subject}"
        return f"مجلس #{self.id}"

    def get_field_value(self, field_key):
        """Get the value of a field by its key"""
        try:
            field_def = FieldDefinition.objects.get(field_key=field_key, is_active=True)
            field_value = self.field_values.get(field=field_def)
            return field_value.value
        except (FieldDefinition.DoesNotExist, FieldValue.DoesNotExist):
            return None

    def days_remaining(self):
        """Calculate days remaining for the council"""
        if self.is_closed:
            return 0

        formation_date = self.get_field_value('formation_date')
        duration = self.get_field_value('duration')

        if not formation_date or not duration:
            return 0

        try:
            formation_date = timezone.datetime.strptime(formation_date, '%Y-%m-%d').date()
            duration = int(duration)
            total_days = duration + (self.extension_days if self.extended else 0)
            end_date = formation_date + timezone.timedelta(days=total_days)
            remaining = (end_date - timezone.now().date()).days
            return max(0, remaining)
        except (ValueError, TypeError):
            return 0

    def is_expired(self):
        """Check if the council is expired"""
        if self.is_closed:
            return False
        return self.days_remaining() <= 0

class FieldValue(models.Model):
    """Model for field values in councils"""
    class Meta:
        app_label = 'lawsuits'
    council = models.ForeignKey(DynamicCouncil, on_delete=models.CASCADE, related_name='field_values', verbose_name="المجلس")
    field = models.ForeignKey(FieldDefinition, on_delete=models.CASCADE, related_name='council_values', verbose_name="الحقل")
    value = models.TextField(verbose_name="القيمة")

    class Meta:
        verbose_name = "قيمة حقل المجلس"
        verbose_name_plural = "قيم حقول المجالس"
        unique_together = ('council', 'field')

    def __str__(self):
        return f"{self.field.name}: {self.value}"

class DynamicCouncilMember(models.Model):
    """Model for dynamic council members"""
    class Meta:
        app_label = 'lawsuits'
    council = models.ForeignKey(DynamicCouncil, on_delete=models.CASCADE, related_name='members', verbose_name="المجلس")
    name = models.CharField(max_length=255, verbose_name="اسم العضو")
    position = models.CharField(max_length=255, verbose_name="مقر العمل")
    is_head = models.BooleanField(default=False, verbose_name="رئيس اللجنة")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_dynamic_council_members', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "عضو مجلس ديناميكي"
        verbose_name_plural = "أعضاء المجالس الديناميكية"

    def __str__(self):
        return f"{self.name} - {self.position}"

class DynamicCouncilDocument(models.Model):
    """Model for dynamic council documents"""
    class Meta:
        app_label = 'lawsuits'
    DOCUMENT_TYPES = (
        ('formation_letter', 'كتاب التشكيل'),
        ('closure_letter', 'كتاب الاغلاق'),
        ('extension_letter', 'كتاب التمديد'),
        ('other', 'أخرى'),
    )

    council = models.ForeignKey(DynamicCouncil, on_delete=models.CASCADE, related_name='documents', verbose_name="المجلس")
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES, verbose_name="نوع المستند")
    document_file = models.FileField(upload_to='dynamic_council_documents/', verbose_name="الملف")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='uploaded_dynamic_council_documents', verbose_name="تم الرفع بواسطة")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الرفع")

    class Meta:
        verbose_name = "مستند المجلس الديناميكي"
        verbose_name_plural = "مستندات المجالس الديناميكية"
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.get_document_type_display()} - {self.council}"
