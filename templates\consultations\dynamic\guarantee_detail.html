{% extends 'base/base.html' %}
{% load consultations_extras %}

{% block title %}تفاصيل الكفالة - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}تفاصيل الكفالة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{% url 'consultations:dynamic_guarantee_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i> العودة إلى القائمة
                    </a>
                </div>
                <div>
                    <a href="{% url 'consultations:dynamic_guarantee_edit' guarantee.id %}" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-2"></i> تعديل
                    </a>
                    <a href="{% url 'consultations:upload_dynamic_guarantee_document' guarantee.id %}" class="btn btn-info me-2">
                        <i class="fas fa-file-upload me-2"></i> رفع مستند
                    </a>
                    <a href="{% url 'consultations:dynamic_guarantee_delete' guarantee.id %}" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i> حذف
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Guarantee Details -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معلومات الكفالة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-12 mb-3">
                            <h6 class="border-bottom pb-2">المعلومات الأساسية</h6>
                        </div>

                        {% for field_value in basic_field_values %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ field_value.field.name }}:</label>
                            <p>
                            {% if field_value.field.field_type == 'boolean' %}
                                {% if field_value.value == 'true' %}
                                    <span class="badge bg-success">نعم</span>
                                {% else %}
                                    <span class="badge bg-secondary">لا</span>
                                {% endif %}
                            {% elif field_value.field.field_type == 'multi_select' %}
                                {% for value in field_value.value|split_string %}
                                    <span class="badge bg-info me-1">{{ value }}</span>
                                {% endfor %}
                            {% else %}
                                {{ field_value.value }}
                            {% endif %}
                            </p>
                        </div>
                        {% endfor %}

                        <!-- Additional Information -->
                        <div class="col-md-12 mb-3">
                            <h6 class="border-bottom pb-2">معلومات إضافية</h6>
                        </div>

                        {% for field_value in additional_field_values %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">{{ field_value.field.name }}:</label>
                            <p>
                            {% if field_value.field.field_type == 'boolean' %}
                                {% if field_value.value == 'true' %}
                                    <span class="badge bg-success">نعم</span>
                                {% else %}
                                    <span class="badge bg-secondary">لا</span>
                                {% endif %}
                            {% elif field_value.field.field_type == 'multi_select' %}
                                {% for value in field_value.value|split_string %}
                                    <span class="badge bg-info me-1">{{ value }}</span>
                                {% endfor %}
                            {% else %}
                                {{ field_value.value }}
                            {% endif %}
                            </p>
                        </div>
                        {% endfor %}

                        <!-- Status -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الحالة:</label>
                            <p>
                                {% if guarantee.is_active %}
                                    {% if guarantee|get_guarantee_field:'expiry_date' < today|date:'Y-m-d' %}
                                        <span class="badge bg-danger">منتهية</span>
                                    {% else %}
                                        <span class="badge bg-success">فعالة</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-secondary">غير فعالة</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <hr>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تم الإنشاء بواسطة:</label>
                            <p>{{ guarantee.created_by.get_full_name }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                            <p>{{ guarantee.created_at }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تم التحديث بواسطة:</label>
                            <p>{{ guarantee.updated_by.get_full_name }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">تاريخ التحديث:</label>
                            <p>{{ guarantee.updated_at }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Documents -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">المستندات</h5>
                    <a href="{% url 'consultations:upload_dynamic_guarantee_document' guarantee.id %}" class="text-white">
                        <i class="fas fa-plus-circle"></i> إضافة مستند
                    </a>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        {% for document in documents %}
                        <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ document.get_document_type_display }}</h6>
                                <p class="mb-1 text-muted small">{{ document.description }}</p>
                                <small class="text-muted">{{ document.uploaded_at }}</small>
                            </div>
                            <div>
                                <a href="{{ document.document_file.url }}" class="btn btn-sm btn-primary" target="_blank" data-bs-toggle="tooltip" title="تنزيل">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-3">
                            <p class="mb-0">لا توجد مستندات</p>
                            <a href="{% url 'consultations:upload_dynamic_guarantee_document' guarantee.id %}" class="btn btn-sm btn-primary mt-2">
                                <i class="fas fa-plus-circle me-2"></i> إضافة مستند
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
