from django.shortcuts import render
from django.contrib.auth.decorators import login_required

@login_required
def index(request):
    """Legal department home page"""
    return render(request, 'legal_department/index.html')

@login_required
def case_list(request):
    """List all cases"""
    return render(request, 'legal_department/case_list.html')

@login_required
def case_create(request):
    """Create a new case"""
    return render(request, 'legal_department/case_form.html')

@login_required
def case_detail(request, case_id):
    """Display case details"""
    return render(request, 'legal_department/case_detail.html')

@login_required
def case_edit(request, case_id):
    """Edit an existing case"""
    return render(request, 'legal_department/case_form.html')

@login_required
def case_delete(request, case_id):
    """Delete a case"""
    return render(request, 'legal_department/case_confirm_delete.html')

@login_required
def case_notes(request, case_id):
    """View case notes"""
    return render(request, 'legal_department/case_notes.html')

@login_required
def add_case_note(request, case_id):
    """Add a note to a case"""
    return render(request, 'legal_department/case_note_form.html')

@login_required
def upload_case_document(request, case_id):
    """Upload a document for a case"""
    return render(request, 'legal_department/upload_document.html')

@login_required
def department_list(request):
    """List all departments"""
    return render(request, 'legal_department/department_list.html')

@login_required
def department_create(request):
    """Create a new department"""
    return render(request, 'legal_department/department_form.html')

@login_required
def department_edit(request, department_id):
    """Edit an existing department"""
    return render(request, 'legal_department/department_form.html')

@login_required
def department_delete(request, department_id):
    """Delete a department"""
    return render(request, 'legal_department/department_confirm_delete.html')
