from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext as _
from .models import AuditLog, UserProfile

def login_view(request):
    """Handle user login"""
    if request.user.is_authenticated:
        return redirect('dashboard:index')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)

        if user is not None:
            login(request, user)

            # Log the login action
            AuditLog.objects.create(
                user=user,
                action='login',
                description=_('تم تسجيل الدخول بنجاح'),
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, _('تم تسجيل الدخول بنجاح'))
            return redirect('dashboard:index')
        else:
            messages.error(request, _('اسم المستخدم أو كلمة المرور غير صحيحة'))

    return render(request, 'base/login.html')

@login_required
def logout_view(request):
    """Handle user logout"""
    # Log the logout action
    AuditLog.objects.create(
        user=request.user,
        action='logout',
        description=_('تم تسجيل الخروج بنجاح'),
        ip_address=request.META.get('REMOTE_ADDR')
    )

    logout(request)
    messages.success(request, _('تم تسجيل الخروج بنجاح'))
    return redirect('core:login')

@login_required
def profile_view(request):
    """Display and update user profile"""
    user = request.user

    try:
        profile = user.profile
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=user)

    if request.method == 'POST':
        # Update profile information
        user.first_name = request.POST.get('first_name')
        user.last_name = request.POST.get('last_name')
        user.email = request.POST.get('email')
        user.save()

        profile.phone = request.POST.get('phone')

        if 'profile_picture' in request.FILES:
            profile.profile_picture = request.FILES['profile_picture']

        profile.save()

        # Log the profile update
        AuditLog.objects.create(
            user=user,
            action='update',
            model_name='UserProfile',
            object_id=profile.id,
            description=_('تم تحديث الملف الشخصي'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث الملف الشخصي بنجاح'))
        return redirect('core:profile')

    context = {
        'user': user,
        'profile': profile
    }

    return render(request, 'core/profile.html', context)
