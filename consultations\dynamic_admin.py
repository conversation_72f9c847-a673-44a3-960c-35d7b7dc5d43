from django.contrib import admin
from .dynamic_models import FieldDefinition, DynamicGuarantee, FieldValue, DynamicGuaranteeDocument

class FieldValueInline(admin.TabularInline):
    model = FieldValue
    extra = 1

class DynamicGuaranteeDocumentInline(admin.TabularInline):
    model = DynamicGuaranteeDocument
    extra = 1

@admin.register(FieldDefinition)
class FieldDefinitionAdmin(admin.ModelAdmin):
    list_display = ('name', 'field_key', 'field_type', 'section', 'is_required', 'is_system', 'is_active', 'display_order')
    list_filter = ('field_type', 'section', 'is_required', 'is_system', 'is_active')
    search_fields = ('name', 'field_key')
    ordering = ('display_order', 'section', 'name')
    
    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(DynamicGuarantee)
class DynamicGuaranteeAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'is_active', 'created_by', 'created_at')
    list_filter = ('is_active', 'created_at')
    inlines = [FieldValueInline, DynamicGuaranteeDocumentInline]
    
    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(FieldValue)
class FieldValueAdmin(admin.ModelAdmin):
    list_display = ('guarantee', 'field', 'value')
    list_filter = ('field',)
    search_fields = ('value',)

@admin.register(DynamicGuaranteeDocument)
class DynamicGuaranteeDocumentAdmin(admin.ModelAdmin):
    list_display = ('guarantee', 'document_type', 'uploaded_by', 'uploaded_at')
    list_filter = ('document_type', 'uploaded_at')
    search_fields = ('description',)
