{% extends 'base/base.html' %}

{% block title %}أعضاء المجلس/اللجنة - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}أعضاء المجلس/اللجنة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{% url 'lawsuits:council_detail' council.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i> العودة إلى تفاصيل المجلس/اللجنة
                    </a>
                </div>
                <div>
                    <a href="{% url 'lawsuits:add_council_member' council.id %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> إضافة عضو جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">أعضاء المجلس/اللجنة</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6>معلومات المجلس/اللجنة:</h6>
                        <p>
                            <strong>نوع المجلس/اللجنة:</strong> {{ council.get_council_type_display }}<br>
                            <strong>أمر التشكيل:</strong> {{ council.formation_order }}<br>
                            <strong>تاريخ التشكيل:</strong> {{ council.formation_date }}
                        </p>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>مقر العمل</th>
                                    <th>المنصب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for member in members %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ member.name }}</td>
                                    <td>{{ member.position }}</td>
                                    <td>
                                        {% if member.is_head %}
                                        <span class="badge bg-primary">رئيس اللجنة</span>
                                        {% else %}
                                        <span class="badge bg-secondary">عضو</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'lawsuits:edit_council_member' council.id member.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'lawsuits:delete_council_member' council.id member.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">لا يوجد أعضاء مسجلين</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
