<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}هيأة المنافذ الحدودية - الدائرة القانونية{% endblock %}</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Custom CSS -->
    {% block extra_css %}{% endblock %}

    <!-- Google Fonts - <PERSON> (Arabic) -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/custom.css">

    <style>
        body {
            font-family: 'Tajawal', sans-serif;
        }

        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }

        .content {
            padding: 20px;
        }

        .navbar-brand img {
            max-height: 50px;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -10px;
            padding: 0.25rem 0.5rem;
            border-radius: 50%;
            font-size: 0.75rem;
            background-color: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            {% if user.is_authenticated %}
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5>هيأة المنافذ الحدودية</h5>
                        <p>الدائرة القانونية</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{% url 'dashboard:index' %}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{% url 'consultations:index' %}">
                                <i class="fas fa-file-contract me-2"></i>
                                الاستشارات والكفالات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{% url 'lawsuits:index' %}">
                                <i class="fas fa-gavel me-2"></i>
                                الدعاوى والمجالس
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{% url 'dashboard:statistics' %}">
                                <i class="fas fa-chart-bar me-2"></i>
                                الإحصائيات والرسوم البيانية
                            </a>
                        </li>
                        {% if user.is_superuser %}
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{% url 'admin:index' %}">
                                <i class="fas fa-cog me-2"></i>
                                إدارة النظام
                            </a>
                        </li>
                        {% endif %}
                        <li class="nav-item">
                            <a class="nav-link text-white" href="{% url 'core:logout' %}">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            {% endif %}

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}{% endblock %}</h1>

                    {% if user.is_authenticated %}
                    <div class="d-flex align-items-center">
                        <!-- Notifications -->
                        <div class="dropdown me-3 position-relative">
                            <a class="btn btn-outline-secondary position-relative" href="#" role="button" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                {% if unread_notifications_count > 0 %}
                                <span class="notification-badge">{{ unread_notifications_count }}</span>
                                {% endif %}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown" style="width: 300px;">
                                <li><h6 class="dropdown-header">الإشعارات</h6></li>
                                <li><hr class="dropdown-divider"></li>
                                {% for notification in request.user.notifications.all|slice:":5" %}
                                <li>
                                    <a class="dropdown-item {% if not notification.is_read %}fw-bold{% endif %}" href="{% if notification.link %}{{ notification.link }}{% else %}#{% endif %}">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>{{ notification.title }}</span>
                                            <span class="badge bg-{{ notification.notification_type }} rounded-pill">{{ notification.get_notification_type_display }}</span>
                                        </div>
                                        <small class="text-muted">{{ notification.created_at|date:"Y-m-d H:i" }}</small>
                                    </a>
                                </li>
                                {% empty %}
                                <li><span class="dropdown-item text-center">لا توجد إشعارات</span></li>
                                {% endfor %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="{% url 'dashboard:notifications' %}">عرض جميع الإشعارات</a></li>
                            </ul>
                        </div>

                        <!-- User Profile -->
                        <div class="dropdown">
                            <a class="btn btn-outline-secondary dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i>
                                {{ user.get_full_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="{% url 'core:profile' %}"><i class="fas fa-id-card me-2"></i> الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'core:logout' %}"><i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج</a></li>
                            </ul>
                        </div>
                    </div>
                    {% endif %}
                </div>

                {% if messages %}
                <div class="messages">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    {% block extra_js %}{% endblock %}
</body>
</html>
