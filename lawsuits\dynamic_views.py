from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext as _
from django.db.models import Q
from django.core.paginator import Paginator
from django.utils import timezone

from .dynamic_models import FieldDefinition, DynamicCouncil, FieldValue, DynamicCouncilMember, DynamicCouncilDocument
from core.models import AuditLog

@login_required
def dynamic_index(request):
    """Dynamic councils home page"""
    # Get counts
    council_count = DynamicCouncil.objects.count()
    active_council_count = DynamicCouncil.objects.filter(is_closed=False).count()
    expired_council_count = sum(1 for council in DynamicCouncil.objects.filter(is_closed=False) if council.is_expired())

    # Get recent councils
    recent_councils = DynamicCouncil.objects.all().order_by('-created_at')[:5]

    context = {
        'council_count': council_count,
        'active_council_count': active_council_count,
        'expired_council_count': expired_council_count,
        'recent_councils': recent_councils,
        'today': timezone.now().date(),
    }

    return render(request, 'lawsuits/dynamic/index.html', context)

@login_required
def field_definition_list(request):
    """List all field definitions"""
    # Check if user is staff
    if not request.user.is_staff:
        messages.error(request, _('ليس لديك صلاحية الوصول إلى هذه الصفحة'))
        return redirect('lawsuits:index')

    # Get all field definitions
    fields = FieldDefinition.objects.all().order_by('section', 'display_order', 'name')

    context = {
        'fields': fields,
    }

    return render(request, 'lawsuits/dynamic/field_definition_list.html', context)

@login_required
def field_definition_create(request):
    """Create a new field definition"""
    # Check if user is staff
    if not request.user.is_staff:
        messages.error(request, _('ليس لديك صلاحية الوصول إلى هذه الصفحة'))
        return redirect('lawsuits:index')

    if request.method == 'POST':
        # Get form data
        name = request.POST.get('name')
        field_key = request.POST.get('field_key')
        field_type = request.POST.get('field_type')
        section = request.POST.get('section')
        description = request.POST.get('description')
        options = request.POST.get('options')
        is_required = request.POST.get('is_required') == 'on'
        is_system = request.POST.get('is_system') == 'on'
        display_order = int(request.POST.get('display_order', 0))

        # Create field definition
        field = FieldDefinition.objects.create(
            name=name,
            field_key=field_key,
            field_type=field_type,
            section=section,
            description=description,
            options=options,
            is_required=is_required,
            is_system=is_system,
            is_active=True,
            display_order=display_order,
            created_by=request.user,
        )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='FieldDefinition',
            object_id=field.id,
            description=_('تم إنشاء تعريف حقل جديد'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إنشاء تعريف الحقل بنجاح'))
        return redirect('lawsuits:field_definition_list')

    context = {
        'field_types': FieldDefinition.FIELD_TYPES,
        'sections': FieldDefinition.SECTIONS,
    }

    return render(request, 'lawsuits/dynamic/field_definition_form.html', context)

@login_required
def field_definition_edit(request, field_id):
    """Edit an existing field definition"""
    # Check if user is staff
    if not request.user.is_staff:
        messages.error(request, _('ليس لديك صلاحية الوصول إلى هذه الصفحة'))
        return redirect('lawsuits:index')

    field = get_object_or_404(FieldDefinition, id=field_id)

    if request.method == 'POST':
        # Get form data
        field.name = request.POST.get('name')
        field.field_key = request.POST.get('field_key')
        field.field_type = request.POST.get('field_type')
        field.section = request.POST.get('section')
        field.description = request.POST.get('description')
        field.options = request.POST.get('options')
        field.is_required = request.POST.get('is_required') == 'on'
        field.is_system = request.POST.get('is_system') == 'on'
        field.is_active = request.POST.get('is_active') == 'on'
        field.display_order = int(request.POST.get('display_order', 0))
        field.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='FieldDefinition',
            object_id=field.id,
            description=_('تم تحديث تعريف الحقل'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث تعريف الحقل بنجاح'))
        return redirect('lawsuits:field_definition_list')

    context = {
        'field': field,
        'field_types': FieldDefinition.FIELD_TYPES,
        'sections': FieldDefinition.SECTIONS,
        'is_edit': True,
    }

    return render(request, 'lawsuits/dynamic/field_definition_form.html', context)

@login_required
def field_definition_delete(request, field_id):
    """Delete a field definition"""
    # Check if user is staff
    if not request.user.is_staff:
        messages.error(request, _('ليس لديك صلاحية الوصول إلى هذه الصفحة'))
        return redirect('lawsuits:index')

    field = get_object_or_404(FieldDefinition, id=field_id)

    # Check if field is used in any council
    if FieldValue.objects.filter(field=field).exists():
        messages.error(request, _('لا يمكن حذف هذا الحقل لأنه مستخدم في مجالس/لجان'))
        return redirect('lawsuits:field_definition_list')

    if request.method == 'POST':
        # Log the action before deletion
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            model_name='FieldDefinition',
            object_id=field.id,
            description=_('تم حذف تعريف الحقل'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        field.delete()
        messages.success(request, _('تم حذف تعريف الحقل بنجاح'))
        return redirect('lawsuits:field_definition_list')

    context = {
        'field': field,
    }

    return render(request, 'lawsuits/dynamic/field_definition_confirm_delete.html', context)

@login_required
def dynamic_council_list(request):
    """List all dynamic councils"""
    # Get filter parameters
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', 'all')
    council_type_filter = request.GET.get('council_type', '')
    start_date = request.GET.get('start_date', '')
    end_date = request.GET.get('end_date', '')

    # Base queryset
    councils = DynamicCouncil.objects.all()

    # Apply filters
    if search_query:
        # Get field definitions for formation order, port name, and subject
        try:
            formation_order_field = FieldDefinition.objects.get(field_key='formation_order')
            port_name_field = FieldDefinition.objects.get(field_key='port_name')
            subject_field = FieldDefinition.objects.get(field_key='subject')

            # Get councils with matching field values
            formation_order_values = FieldValue.objects.filter(field=formation_order_field, value__icontains=search_query).values_list('council_id', flat=True)
            port_name_values = FieldValue.objects.filter(field=port_name_field, value__icontains=search_query).values_list('council_id', flat=True)
            subject_values = FieldValue.objects.filter(field=subject_field, value__icontains=search_query).values_list('council_id', flat=True)

            # Combine the results
            councils = councils.filter(Q(id__in=formation_order_values) | Q(id__in=port_name_values) | Q(id__in=subject_values))
        except FieldDefinition.DoesNotExist:
            # If field definitions don't exist, return empty queryset
            councils = DynamicCouncil.objects.none()

    if status_filter == 'active':
        councils = councils.filter(is_closed=False)
    elif status_filter == 'closed':
        councils = councils.filter(is_closed=True)
    elif status_filter == 'expired':
        # We need to filter in Python because is_expired is a method, not a field
        if isinstance(councils, list):
            councils = [council for council in councils if council.is_expired()]
        else:
            councils = [council for council in councils if council.is_expired()]

    if council_type_filter:
        # Get field definition for council type
        try:
            council_type_field = FieldDefinition.objects.get(field_key='council_type')
            council_type_values = FieldValue.objects.filter(field=council_type_field, value=council_type_filter).values_list('council_id', flat=True)
            councils = councils.filter(id__in=council_type_values)
        except FieldDefinition.DoesNotExist:
            pass

    if start_date:
        # Get field definition for formation date
        try:
            formation_date_field = FieldDefinition.objects.get(field_key='formation_date')
            formation_date_values = FieldValue.objects.filter(field=formation_date_field, value__gte=start_date).values_list('council_id', flat=True)
            councils = councils.filter(id__in=formation_date_values)
        except FieldDefinition.DoesNotExist:
            pass

    if end_date:
        # Get field definition for formation date
        try:
            formation_date_field = FieldDefinition.objects.get(field_key='formation_date')
            formation_date_values = FieldValue.objects.filter(field=formation_date_field, value__lte=end_date).values_list('council_id', flat=True)
            councils = councils.filter(id__in=formation_date_values)
        except FieldDefinition.DoesNotExist:
            pass

    # Order by
    councils = councils.order_by('-created_at')

    # Pagination
    paginator = Paginator(councils, 10)  # Show 10 councils per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get council types for filter
    try:
        council_type_field = FieldDefinition.objects.get(field_key='council_type')
        council_types = council_type_field.get_options_list()
    except FieldDefinition.DoesNotExist:
        council_types = []

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'council_type_filter': council_type_filter,
        'start_date': start_date,
        'end_date': end_date,
        'today': timezone.now().date(),
        'council_types': council_types,
    }

    return render(request, 'lawsuits/dynamic/council_list.html', context)

@login_required
def dynamic_council_create(request):
    """Create a new dynamic council"""
    # Get all active field definitions
    fields = FieldDefinition.objects.filter(is_active=True).order_by('section', 'display_order', 'name')

    if request.method == 'POST':
        # Create council
        council = DynamicCouncil.objects.create(
            is_closed=False,
            created_by=request.user,
            updated_by=request.user,
        )

        # Process field values
        for field in fields:
            field_key = f'field_{field.field_key}'

            if field.field_type == 'boolean':
                # For boolean fields, we only get a value if the checkbox is checked
                if field_key in request.POST:
                    value = 'true'
                else:
                    value = 'false'

                FieldValue.objects.create(
                    council=council,
                    field=field,
                    value=value
                )

            elif field.field_type == 'multi_select':
                # For multi-select fields, we get a list of values
                values = request.POST.getlist(field_key)
                if values:
                    # Store as comma-separated string
                    FieldValue.objects.create(
                        council=council,
                        field=field,
                        value=','.join(values)
                    )

            else:
                # For all other fields
                field_value = request.POST.get(field_key)
                if field_value:
                    FieldValue.objects.create(
                        council=council,
                        field=field,
                        value=field_value
                    )

        # Handle council members
        member_count = int(request.POST.get('member_count', 0))
        for i in range(member_count):
            name = request.POST.get(f'member_name_{i}')
            position = request.POST.get(f'member_position_{i}')
            is_head = request.POST.get(f'member_is_head_{i}') == 'on'

            if name and position:
                DynamicCouncilMember.objects.create(
                    council=council,
                    name=name,
                    position=position,
                    is_head=is_head,
                    created_by=request.user
                )

        # Handle formation letter document
        if 'formation_letter' in request.FILES:
            DynamicCouncilDocument.objects.create(
                council=council,
                document_type='formation_letter',
                document_file=request.FILES['formation_letter'],
                description=_('كتاب التشكيل'),
                uploaded_by=request.user
            )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='DynamicCouncil',
            object_id=council.id,
            description=_('تم إنشاء مجلس/لجنة جديدة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إنشاء المجلس/اللجنة بنجاح'))
        return redirect('lawsuits:dynamic_council_detail', council_id=council.id)

    # Group fields by section
    basic_fields = fields.filter(section='basic')
    additional_fields = fields.filter(section='additional')

    context = {
        'basic_fields': basic_fields,
        'additional_fields': additional_fields,
        'is_edit': False,
    }

    return render(request, 'lawsuits/dynamic/council_form.html', context)

@login_required
def dynamic_council_detail(request, council_id):
    """Display dynamic council details"""
    council = get_object_or_404(DynamicCouncil, id=council_id)
    members = council.members.all()
    documents = council.documents.all()
    field_values = council.field_values.all().select_related('field')

    # Group field values by section
    basic_field_values = [fv for fv in field_values if fv.field.section == 'basic']
    additional_field_values = [fv for fv in field_values if fv.field.section == 'additional']

    context = {
        'council': council,
        'members': members,
        'documents': documents,
        'basic_field_values': basic_field_values,
        'additional_field_values': additional_field_values,
        'today': timezone.now().date(),
        'days_remaining': council.days_remaining(),
        'is_expired': council.is_expired(),
    }

    return render(request, 'lawsuits/dynamic/council_detail.html', context)

@login_required
def dynamic_council_edit(request, council_id):
    """Edit an existing dynamic council"""
    council = get_object_or_404(DynamicCouncil, id=council_id)
    fields = FieldDefinition.objects.filter(is_active=True).order_by('section', 'display_order', 'name')
    field_values = {fv.field.field_key: fv.value for fv in council.field_values.all().select_related('field')}

    if request.method == 'POST':
        # Update council
        council.is_closed = 'is_closed' in request.POST
        council.updated_by = request.user
        council.save()

        # Update field values
        for field in fields:
            field_key = f'field_{field.field_key}'

            if field.field_type == 'boolean':
                # For boolean fields, we only get a value if the checkbox is checked
                if field_key in request.POST:
                    value = 'true'
                else:
                    value = 'false'

                FieldValue.objects.update_or_create(
                    council=council,
                    field=field,
                    defaults={'value': value}
                )

            elif field.field_type == 'multi_select':
                # For multi-select fields, we get a list of values
                values = request.POST.getlist(field_key)
                if values:
                    # Store as comma-separated string
                    FieldValue.objects.update_or_create(
                        council=council,
                        field=field,
                        defaults={'value': ','.join(values)}
                    )

            else:
                # For all other fields
                field_value = request.POST.get(field_key)
                if field_value:
                    FieldValue.objects.update_or_create(
                        council=council,
                        field=field,
                        defaults={'value': field_value}
                    )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='DynamicCouncil',
            object_id=council.id,
            description=_('تم تحديث المجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث المجلس/اللجنة بنجاح'))
        return redirect('lawsuits:dynamic_council_detail', council_id=council.id)

    # Group fields by section
    basic_fields = fields.filter(section='basic')
    additional_fields = fields.filter(section='additional')

    context = {
        'council': council,
        'basic_fields': basic_fields,
        'additional_fields': additional_fields,
        'field_values': field_values,
        'is_edit': True,
    }

    return render(request, 'lawsuits/dynamic/council_form.html', context)

@login_required
def dynamic_council_delete(request, council_id):
    """Delete a dynamic council"""
    council = get_object_or_404(DynamicCouncil, id=council_id)

    if request.method == 'POST':
        # Log the action before deletion
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            model_name='DynamicCouncil',
            object_id=council.id,
            description=_('تم حذف المجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        council.delete()
        messages.success(request, _('تم حذف المجلس/اللجنة بنجاح'))
        return redirect('lawsuits:dynamic_council_list')

    context = {
        'council': council,
    }

    return render(request, 'lawsuits/dynamic/council_confirm_delete.html', context)

@login_required
def dynamic_council_members(request, council_id):
    """Manage dynamic council members"""
    council = get_object_or_404(DynamicCouncil, id=council_id)
    members = council.members.all()

    context = {
        'council': council,
        'members': members,
    }

    return render(request, 'lawsuits/dynamic/council_members.html', context)

@login_required
def add_dynamic_council_member(request, council_id):
    """Add a dynamic council member"""
    council = get_object_or_404(DynamicCouncil, id=council_id)

    if request.method == 'POST':
        name = request.POST.get('name')
        position = request.POST.get('position')
        is_head = request.POST.get('is_head') == 'on'

        # If this member is head, unset other heads
        if is_head:
            council.members.filter(is_head=True).update(is_head=False)

        # Create member
        member = DynamicCouncilMember.objects.create(
            council=council,
            name=name,
            position=position,
            is_head=is_head,
            created_by=request.user
        )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='DynamicCouncilMember',
            object_id=member.id,
            description=_('تم إضافة عضو جديد للمجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إضافة العضو بنجاح'))
        return redirect('lawsuits:dynamic_council_members', council_id=council.id)

    context = {
        'council': council,
    }

    return render(request, 'lawsuits/dynamic/council_member_form.html', context)

@login_required
def edit_dynamic_council_member(request, council_id, member_id):
    """Edit a dynamic council member"""
    council = get_object_or_404(DynamicCouncil, id=council_id)
    member = get_object_or_404(DynamicCouncilMember, id=member_id, council=council)

    if request.method == 'POST':
        name = request.POST.get('name')
        position = request.POST.get('position')
        is_head = request.POST.get('is_head') == 'on'

        # If this member is head, unset other heads
        if is_head and not member.is_head:
            council.members.filter(is_head=True).update(is_head=False)

        # Update member
        member.name = name
        member.position = position
        member.is_head = is_head
        member.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='DynamicCouncilMember',
            object_id=member.id,
            description=_('تم تحديث عضو المجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث العضو بنجاح'))
        return redirect('lawsuits:dynamic_council_members', council_id=council.id)

    context = {
        'council': council,
        'member': member,
        'is_edit': True,
    }

    return render(request, 'lawsuits/dynamic/council_member_form.html', context)

@login_required
def delete_dynamic_council_member(request, council_id, member_id):
    """Delete a dynamic council member"""
    council = get_object_or_404(DynamicCouncil, id=council_id)
    member = get_object_or_404(DynamicCouncilMember, id=member_id, council=council)

    if request.method == 'POST':
        # Log the action before deletion
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            model_name='DynamicCouncilMember',
            object_id=member.id,
            description=_('تم حذف عضو المجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        member.delete()
        messages.success(request, _('تم حذف العضو بنجاح'))
        return redirect('lawsuits:dynamic_council_members', council_id=council.id)

    context = {
        'council': council,
        'member': member,
    }

    return render(request, 'lawsuits/dynamic/council_member_confirm_delete.html', context)

@login_required
def upload_dynamic_council_document(request, council_id):
    """Upload a document for a dynamic council"""
    council = get_object_or_404(DynamicCouncil, id=council_id)

    if request.method == 'POST':
        document_type = request.POST.get('document_type')
        description = request.POST.get('description')
        document_file = request.FILES.get('document_file')

        if document_file:
            document = DynamicCouncilDocument.objects.create(
                council=council,
                document_type=document_type,
                document_file=document_file,
                description=description,
                uploaded_by=request.user
            )

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='create',
                model_name='DynamicCouncilDocument',
                object_id=document.id,
                description=_('تم رفع مستند جديد للمجلس/اللجنة'),
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, _('تم رفع المستند بنجاح'))
        else:
            messages.error(request, _('يرجى اختيار ملف لرفعه'))

        return redirect('lawsuits:dynamic_council_detail', council_id=council.id)

    context = {
        'council': council,
        'document_types': DynamicCouncilDocument.DOCUMENT_TYPES,
    }

    return render(request, 'lawsuits/dynamic/upload_document.html', context)

@login_required
def initialize_council_fields(request):
    """Initialize default field definitions for councils"""
    # Check if user is staff
    if not request.user.is_staff:
        messages.error(request, _('ليس لديك صلاحية الوصول إلى هذه الصفحة'))
        return redirect('lawsuits:index')

    # Define default fields
    default_fields = [
        # Basic information fields
        {
            'name': 'نوع المجلس',
            'field_key': 'council_type',
            'field_type': 'select',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 1,
            'options': 'مجلس تحقيقي,لجنة تحقيقية,لجنة مشتركة',
        },
        {
            'name': 'امر التشكيل',
            'field_key': 'formation_order',
            'field_type': 'text',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 2,
        },
        {
            'name': 'تاريخ امر التشكيل',
            'field_key': 'formation_date',
            'field_type': 'date',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 3,
        },
        {
            'name': 'اسم المنفذ أو الدائرة',
            'field_key': 'port_name',
            'field_type': 'text',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 4,
        },
        {
            'name': 'المدة المحددة لإنهاء اللجنة (بالأيام)',
            'field_key': 'duration',
            'field_type': 'number',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 5,
        },
        {
            'name': 'موضوع اللجنة/ المجلس التحقيقي',
            'field_key': 'subject',
            'field_type': 'text',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 6,
        },

        # Additional information fields
        {
            'name': 'اسم ورقم اضبارة الحفظ',
            'field_key': 'file_name',
            'field_type': 'text',
            'section': 'additional',
            'is_required': False,
            'is_system': True,
            'display_order': 1,
        },
        {
            'name': 'اخر اجراء',
            'field_key': 'last_action',
            'field_type': 'text',
            'section': 'additional',
            'is_required': False,
            'is_system': True,
            'display_order': 2,
        },
        {
            'name': 'الملاحظات',
            'field_key': 'notes',
            'field_type': 'text',
            'section': 'additional',
            'is_required': False,
            'is_system': True,
            'display_order': 3,
        },
    ]

    # Create fields if they don't exist
    created_count = 0
    for field_data in default_fields:
        field_key = field_data.pop('field_key')
        options = field_data.pop('options', None)

        # Check if field already exists
        if not FieldDefinition.objects.filter(field_key=field_key).exists():
            # Create field
            field = FieldDefinition.objects.create(
                field_key=field_key,
                options=options,
                created_by=request.user,
                **field_data
            )
            created_count += 1

    if created_count > 0:
        messages.success(request, _(f'تم إنشاء {created_count} حقل افتراضي بنجاح'))
    else:
        messages.info(request, _('جميع الحقول الافتراضية موجودة بالفعل'))

    return redirect('lawsuits:field_definition_list')