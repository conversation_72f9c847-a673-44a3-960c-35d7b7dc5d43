{% extends 'base/base.html' %}

{% block title %}
{% if is_edit %}
تعديل تعريف حقل - {{ field.name }}
{% else %}
إضافة تعريف حقل جديد
{% endif %}
{% endblock %}

{% block page_title %}
{% if is_edit %}
تعديل تعريف حقل - {{ field.name }}
{% else %}
إضافة تعريف حقل جديد
{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        {% if is_edit %}
                        تعديل تعريف حقل
                        {% else %}
                        إضافة تعريف حقل جديد
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">اسم الحقل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ field.name|default:'' }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="field_key" class="form-label">مفتاح الحقل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="field_key" name="field_key" value="{{ field.field_key|default:'' }}" required {% if is_edit %}readonly{% endif %}>
                                <small class="text-muted">يجب أن يكون المفتاح فريدًا ويحتوي على أحرف إنجليزية وأرقام وشرطات سفلية فقط</small>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="field_type" class="form-label">نوع الحقل <span class="text-danger">*</span></label>
                                <select class="form-select" id="field_type" name="field_type" required>
                                    <option value="">-- اختر نوع الحقل --</option>
                                    {% for type_value, type_name in field_types %}
                                    <option value="{{ type_value }}" {% if field.field_type == type_value %}selected{% endif %}>{{ type_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="section" class="form-label">القسم <span class="text-danger">*</span></label>
                                <select class="form-select" id="section" name="section" required>
                                    <option value="">-- اختر القسم --</option>
                                    {% for section_value, section_name in sections %}
                                    <option value="{{ section_value }}" {% if field.section == section_value %}selected{% endif %}>{{ section_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="description" class="form-label">وصف الحقل</label>
                                <textarea class="form-control" id="description" name="description" rows="3">{{ field.description|default:'' }}</textarea>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="options" class="form-label">خيارات الحقل (للقوائم المنسدلة)</label>
                                <textarea class="form-control" id="options" name="options" rows="3">{{ field.options|default:'' }}</textarea>
                                <small class="text-muted">أدخل الخيارات مفصولة بفواصل (مثال: خيار1,خيار2,خيار3)</small>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_required" name="is_required" {% if field.is_required %}checked{% endif %}>
                                    <label class="form-check-label" for="is_required">
                                        مطلوب
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_system" name="is_system" {% if field.is_system %}checked{% endif %}>
                                    <label class="form-check-label" for="is_system">
                                        حقل نظام
                                    </label>
                                </div>
                            </div>
                            {% if is_edit %}
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if field.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        فعال
                                    </label>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="display_order" class="form-label">ترتيب العرض</label>
                                <input type="number" class="form-control" id="display_order" name="display_order" value="{{ field.display_order|default:'0' }}" min="0">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    {% if is_edit %}
                                    حفظ التغييرات
                                    {% else %}
                                    إضافة الحقل
                                    {% endif %}
                                </button>
                                <a href="{% url 'legal_department:field_definition_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const fieldTypeSelect = document.getElementById('field_type');
        const optionsTextarea = document.getElementById('options');
        const optionsRow = optionsTextarea.closest('.row');
        
        function toggleOptionsVisibility() {
            const selectedType = fieldTypeSelect.value;
            if (selectedType === 'select' || selectedType === 'multi_select') {
                optionsRow.style.display = 'flex';
            } else {
                optionsRow.style.display = 'none';
            }
        }
        
        // Initial check
        toggleOptionsVisibility();
        
        // Add event listener
        fieldTypeSelect.addEventListener('change', toggleOptionsVisibility);
    });
</script>
{% endblock %}
{% endblock %}
