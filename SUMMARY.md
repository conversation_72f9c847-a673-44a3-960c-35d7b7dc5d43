# ملخص نظام إدارة الدائرة القانونية

## نظرة عامة

تم بناء نظام إدارة الدائرة القانونية لهيأة المنافذ الحدودية باستخدام إطار عمل Django مع واجهة مستخدم عربية بالكامل. يتكون النظام من عدة وحدات رئيسية تغطي مختلف جوانب العمل القانوني في الهيأة.

## الوحدات الرئيسية

### 1. وحدة المستخدمين والإعدادات (Core)

- إدارة المستخدمين مع مستويات صلاحيات مختلفة (مدير النظام، رئيس قسم، مدخل بيانات، المدير العام)
- إدارة الأقسام والإعدادات العامة للنظام
- تسجيل الدخول والخروج وإدارة الملفات الشخصية
- سجل التدقيق لتتبع جميع الإجراءات في النظام

### 2. وحدة الاستشارات والكفالات (Consultations)

- إدارة الكفالات والضمانات
- تتبع تواريخ إصدار وانتهاء الكفالات
- إدارة مستندات الكفالات
- إمكانية إضافة حقول مخصصة للكفالات

### 3. وحدة الدعاوى والمجالس (Lawsuits)

- إدارة المجالس واللجان التحقيقية
- تتبع مدة المجالس/اللجان وتنبيهات للمتجاوزة للمدة
- إدارة أعضاء المجالس/اللجان
- إمكانية تمديد وإغلاق المجالس/اللجان
- إدارة مستندات المجالس/اللجان

### 4. وحدة لوحة التحكم (Dashboard)

- عرض إحصائيات ومؤشرات الأداء الرئيسية
- إدارة الإشعارات
- إنشاء وعرض التقارير
- إدارة النسخ الاحتياطي والاستعادة

### 5. وحدة الدائرة القانونية (Legal Department)

- إدارة القضايا القانونية
- ربط القضايا بالأقسام والمستخدمين
- إدارة مستندات القضايا
- إضافة ملاحظات للقضايا

## الميزات التقنية

- واجهة مستخدم عربية بالكامل
- تصميم متجاوب باستخدام Bootstrap 5
- دعم رفع وتنزيل المستندات
- نظام إشعارات متكامل
- تقارير وإحصائيات متقدمة
- نظام بحث وتصفية متقدم
- أمان وتحكم في الصلاحيات

## الاستخدام

يمكن استخدام النظام من قبل:

1. **مدير النظام**: للوصول الكامل وإدارة جميع جوانب النظام
2. **رؤساء الأقسام**: لإدارة الكفالات والمجالس والقضايا في أقسامهم
3. **مدخلي البيانات**: لإدخال البيانات والمستندات
4. **المدير العام**: لعرض التقارير والإحصائيات

## التطوير المستقبلي

يمكن تطوير النظام مستقبلاً بإضافة:

1. واجهة برمجة تطبيقات (API) للتكامل مع أنظمة أخرى
2. تطبيق للهواتف الذكية
3. نظام أرشفة إلكترونية متكامل
4. دعم متعدد اللغات (إضافة اللغة الإنجليزية)
5. نظام سير العمل (Workflow) لتتبع مراحل القضايا والمجالس
