{% extends 'base/base.html' %}
{% load lawsuits_extras %}

{% block title %}
{% if is_edit %}
تعديل مجلس/لجنة
{% else %}
إضافة مجلس/لجنة جديدة
{% endif %}
{% endblock %}

{% block page_title %}
{% if is_edit %}
تعديل مجلس/لجنة
{% else %}
إضافة مجلس/لجنة جديدة
{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        {% if is_edit %}
                        تعديل مجلس/لجنة
                        {% else %}
                        إضافة مجلس/لجنة جديدة
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        {% if form.errors %}
                        <div class="alert alert-danger">
                            <h4 class="alert-heading">يوجد أخطاء في النموذج:</h4>
                            <ul>
                                {% for field in form %}
                                    {% for error in field.errors %}
                                    <li>{{ field.label }}: {{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                                {% for error in form.non_field_errors %}
                                <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <!-- Basic Information -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">معلومات أساسية</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for field in basic_fields %}
                                    <div class="col-md-4 mb-3">
                                        <label for="field_{{ field.field_key }}" class="form-label">
                                            {{ field.name }}
                                            {% if field.is_required %}<span class="text-danger">*</span>{% endif %}
                                        </label>

                                        {% if field.field_type == 'text' %}
                                        <input type="text" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                               value="{{ council|get_council_field:field.field_key }}"
                                               {% if field.is_required %}required{% endif %}>

                                        {% elif field.field_type == 'number' %}
                                        <input type="number" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                               value="{{ council|get_council_field:field.field_key }}"
                                               {% if field.is_required %}required{% endif %}>

                                        {% elif field.field_type == 'date' %}
                                        <input type="date" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                               value="{{ council|get_council_field:field.field_key|date:'Y-m-d' }}"
                                               {% if field.is_required %}required{% endif %}>

                                        {% elif field.field_type == 'textarea' %}
                                        <textarea class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" rows="3"
                                                  {% if field.is_required %}required{% endif %}>{{ council|get_council_field:field.field_key }}</textarea>

                                        {% elif field.field_type == 'select' %}
                                        <select class="form-select" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                                {% if field.is_required %}required{% endif %}>
                                            <option value="">-- اختر {{ field.name }} --</option>
                                            {% for option in field.get_options_list %}
                                            <option value="{{ option }}" {% if council|get_council_field:field.field_key == option %}selected{% endif %}>{{ option }}</option>
                                            {% endfor %}
                                        </select>

                                        {% elif field.field_type == 'multi_select' %}
                                        <select class="form-select" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" multiple
                                                {% if field.is_required %}required{% endif %}>
                                            {% for option in field.get_options_list %}
                                            <option value="{{ option }}" {% if option in council|get_council_field:field.field_key %}selected{% endif %}>{{ option }}</option>
                                            {% endfor %}
                                        </select>

                                        {% elif field.field_type == 'boolean' %}
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                                   {% if council|get_council_field:field.field_key %}checked{% endif %}>
                                            <label class="form-check-label" for="field_{{ field.field_key }}">
                                                {{ field.name }}
                                            </label>
                                        </div>

                                        {% elif field.field_type == 'file' %}
                                        <input type="file" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                               {% if field.is_required and not is_edit %}required{% endif %}>
                                        {% if council|get_council_field:field.field_key %}
                                        <div class="mt-2">
                                            <a href="{{ council|get_council_field:field.field_key.url }}" target="_blank">عرض الملف الحالي</a>
                                        </div>
                                        {% endif %}

                                        {% endif %}

                                        {% if field.description %}
                                        <div class="form-text">{{ field.description }}</div>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information -->
                        {% if additional_fields %}
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">معلومات إضافية</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for field in additional_fields %}
                                    <div class="col-md-4 mb-3">
                                        <label for="field_{{ field.field_key }}" class="form-label">
                                            {{ field.name }}
                                            {% if field.is_required %}<span class="text-danger">*</span>{% endif %}
                                        </label>

                                        {% if field.field_type == 'text' %}
                                        <input type="text" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                               value="{{ council|get_council_field:field.field_key }}"
                                               {% if field.is_required %}required{% endif %}>

                                        {% elif field.field_type == 'number' %}
                                        <input type="number" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                               value="{{ council|get_council_field:field.field_key }}"
                                               {% if field.is_required %}required{% endif %}>

                                        {% elif field.field_type == 'date' %}
                                        <input type="date" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                               value="{{ council|get_council_field:field.field_key|date:'Y-m-d' }}"
                                               {% if field.is_required %}required{% endif %}>

                                        {% elif field.field_type == 'textarea' %}
                                        <textarea class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" rows="3"
                                                  {% if field.is_required %}required{% endif %}>{{ council|get_council_field:field.field_key }}</textarea>

                                        {% elif field.field_type == 'select' %}
                                        <select class="form-select" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                                {% if field.is_required %}required{% endif %}>
                                            <option value="">-- اختر {{ field.name }} --</option>
                                            {% for option in field.get_options_list %}
                                            <option value="{{ option }}" {% if council|get_council_field:field.field_key == option %}selected{% endif %}>{{ option }}</option>
                                            {% endfor %}
                                        </select>

                                        {% elif field.field_type == 'multi_select' %}
                                        <select class="form-select" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" multiple
                                                {% if field.is_required %}required{% endif %}>
                                            {% for option in field.get_options_list %}
                                            <option value="{{ option }}" {% if option in council|get_council_field:field.field_key %}selected{% endif %}>{{ option }}</option>
                                            {% endfor %}
                                        </select>

                                        {% elif field.field_type == 'boolean' %}
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                                   {% if council|get_council_field:field.field_key %}checked{% endif %}>
                                            <label class="form-check-label" for="field_{{ field.field_key }}">
                                                {{ field.name }}
                                            </label>
                                        </div>

                                        {% elif field.field_type == 'file' %}
                                        <input type="file" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}"
                                               {% if field.is_required and not is_edit %}required{% endif %}>
                                        {% if council|get_council_field:field.field_key %}
                                        <div class="mt-2">
                                            <a href="{{ council|get_council_field:field.field_key.url }}" target="_blank">عرض الملف الحالي</a>
                                        </div>
                                        {% endif %}

                                        {% endif %}

                                        {% if field.description %}
                                        <div class="form-text">{{ field.description }}</div>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Council Status -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">حالة المجلس/اللجنة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_closed" name="is_closed"
                                                   {% if council.is_closed %}checked{% endif %}>
                                            <label class="form-check-label" for="is_closed">
                                                مغلق
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="extended" name="extended"
                                                   {% if council.extended %}checked{% endif %}>
                                            <label class="form-check-label" for="extended">
                                                تم التمديد
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="extension_days" class="form-label">أيام التمديد</label>
                                        <input type="number" class="form-control" id="extension_days" name="extension_days"
                                               value="{{ council.extension_days|default:0 }}" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    {% if is_edit %}
                                    حفظ التغييرات
                                    {% else %}
                                    إضافة المجلس/اللجنة
                                    {% endif %}
                                </button>
                                <a href="{% url 'lawsuits:dynamic_council_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize multi-select fields
        const multiSelects = document.querySelectorAll('select[multiple]');
        if (multiSelects.length > 0) {
            // If you have a multi-select library, initialize it here
        }

        // Toggle extension days based on extended checkbox
        const extendedCheckbox = document.getElementById('extended');
        const extensionDaysInput = document.getElementById('extension_days');

        function toggleExtensionDays() {
            if (extendedCheckbox.checked) {
                extensionDaysInput.removeAttribute('disabled');
            } else {
                extensionDaysInput.setAttribute('disabled', 'disabled');
                extensionDaysInput.value = '0';
            }
        }

        // Initial check
        toggleExtensionDays();

        // Add event listener
        extendedCheckbox.addEventListener('change', toggleExtensionDays);
    });
</script>
{% endblock %}
{% endblock %}
