{% extends 'base/base.html' %}

{% block title %}الإشعارات - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}الإشعارات{% endblock %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">جميع الإشعارات</h5>
            <div>
                <form method="post" class="d-inline">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="mark_all_read">
                    <button type="submit" class="btn btn-sm btn-light me-2">
                        <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                    </button>
                </form>
                <form method="post" class="d-inline">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="delete_all">
                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف جميع الإشعارات؟')">
                        <i class="fas fa-trash"></i> حذف الكل
                    </button>
                </form>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>العنوان</th>
                            <th>الرسالة</th>
                            <th>النوع</th>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for notification in notifications %}
                        <tr {% if not notification.is_read %}class="table-primary"{% endif %}>
                            <td>{{ notification.title }}</td>
                            <td>{{ notification.message }}</td>
                            <td>
                                <span class="badge bg-{{ notification.notification_type }}">
                                    {{ notification.get_notification_type_display }}
                                </span>
                            </td>
                            <td>{{ notification.created_at }}</td>
                            <td>
                                {% if notification.is_read %}
                                <span class="badge bg-secondary">تمت القراءة</span>
                                {% else %}
                                <span class="badge bg-warning">جديد</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    {% if notification.link %}
                                    <a href="{{ notification.link }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    {% endif %}
                                    
                                    {% if not notification.is_read %}
                                    <form method="post" class="d-inline">
                                        {% csrf_token %}
                                        <input type="hidden" name="action" value="mark_read">
                                        <input type="hidden" name="notification_id" value="{{ notification.id }}">
                                        <button type="submit" class="btn btn-sm btn-success">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                    {% endif %}
                                    
                                    <form method="post" class="d-inline">
                                        {% csrf_token %}
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="notification_id" value="{{ notification.id }}">
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">لا توجد إشعارات</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
