from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """Get an item from a dictionary by key"""
    return dictionary.get(key)

@register.filter
def get_guarantee_field(guarantee, field_key):
    """Get a field value from a guarantee by field key"""
    return guarantee.get_field_value(field_key)

@register.filter
def split_string(value, delimiter=','):
    """Split a string by delimiter"""
    if value:
        return value.split(delimiter)
    return []
