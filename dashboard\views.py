from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext as _
from django.http import HttpResponse
from django.db.models import Count, Q, Sum, Avg, F, ExpressionWrapper, fields, DateField
from django.utils import timezone
from django.db.models.functions import <PERSON>runcMonth, TruncYear
import json
import os
import tempfile
import zipfile
import datetime

from .models import Notification, Report, Backup
from consultations.models import Guarantee
from lawsuits.models import Council
from core.models import AuditLog, Department, UserProfile
from legal_department.models import LegalCase

@login_required
def index(request):
    """Dashboard home page"""
    # Get user profile
    try:
        profile = request.user.profile
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=request.user)

    # Get counts for different models
    guarantee_count = Guarantee.objects.count()
    council_count = Council.objects.count()
    active_council_count = Council.objects.filter(is_closed=False).count()
    expired_council_count = sum(1 for council in Council.objects.filter(is_closed=False) if council.is_expired())
    case_count = LegalCase.objects.count()
    department_count = Department.objects.count()

    # Get recent notifications
    notifications = Notification.objects.filter(user=request.user, is_read=False)[:5]

    # Get recent audit logs
    audit_logs = AuditLog.objects.all().order_by('-timestamp')[:10]

    context = {
        'guarantee_count': guarantee_count,
        'council_count': council_count,
        'active_council_count': active_council_count,
        'expired_council_count': expired_council_count,
        'case_count': case_count,
        'department_count': department_count,
        'notifications': notifications,
        'audit_logs': audit_logs,
        'profile': profile,
    }

    return render(request, 'dashboard/index.html', context)

@login_required
def notifications(request):
    """View and manage notifications"""
    notifications = Notification.objects.filter(user=request.user).order_by('-created_at')

    if request.method == 'POST':
        action = request.POST.get('action')
        notification_id = request.POST.get('notification_id')

        if action == 'mark_read' and notification_id:
            notification = get_object_or_404(Notification, id=notification_id, user=request.user)
            notification.is_read = True
            notification.save()
            messages.success(request, _('تم تحديث حالة الإشعار'))

        elif action == 'mark_all_read':
            Notification.objects.filter(user=request.user, is_read=False).update(is_read=True)
            messages.success(request, _('تم تحديث حالة جميع الإشعارات'))

        elif action == 'delete' and notification_id:
            notification = get_object_or_404(Notification, id=notification_id, user=request.user)
            notification.delete()
            messages.success(request, _('تم حذف الإشعار'))

        elif action == 'delete_all':
            Notification.objects.filter(user=request.user).delete()
            messages.success(request, _('تم حذف جميع الإشعارات'))

        return redirect('dashboard:notifications')

    context = {
        'notifications': notifications,
    }

    return render(request, 'dashboard/notifications.html', context)

@login_required
def reports(request):
    """Generate and view reports"""
    reports = Report.objects.filter(created_by=request.user).order_by('-created_at')

    if request.method == 'POST':
        report_type = request.POST.get('report_type')
        report_name = request.POST.get('report_name')
        description = request.POST.get('description')

        # Get query parameters based on report type
        query_params = {}

        if report_type == 'guarantee':
            # Get guarantee report parameters
            start_date = request.POST.get('start_date')
            end_date = request.POST.get('end_date')
            bank_name = request.POST.get('bank_name')
            is_active = request.POST.get('is_active')

            query_params = {
                'start_date': start_date,
                'end_date': end_date,
                'bank_name': bank_name,
                'is_active': is_active == 'on',
            }

        elif report_type == 'council':
            # Get council report parameters
            council_type = request.POST.get('council_type')
            is_closed = request.POST.get('is_closed')
            is_expired = request.POST.get('is_expired')

            query_params = {
                'council_type': council_type,
                'is_closed': is_closed == 'on',
                'is_expired': is_expired == 'on',
            }

        # Create the report
        report = Report.objects.create(
            name=report_name,
            report_type=report_type,
            query_params=query_params,
            description=description,
            created_by=request.user,
        )

        messages.success(request, _('تم إنشاء التقرير بنجاح'))
        return redirect('dashboard:reports')

    context = {
        'reports': reports,
    }

    return render(request, 'dashboard/reports.html', context)

@login_required
def backups(request):
    """View and manage backups"""
    if not request.user.is_superuser:
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة'))
        return redirect('dashboard:index')

    backups = Backup.objects.all().order_by('-created_at')

    context = {
        'backups': backups,
    }

    return render(request, 'dashboard/backups.html', context)

@login_required
def create_backup(request):
    """Create a new backup"""
    if not request.user.is_superuser:
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة'))
        return redirect('dashboard:index')

    if request.method == 'POST':
        name = request.POST.get('name')
        description = request.POST.get('description')

        # Create a backup file (this is a simplified version, in a real app you would use Django's dumpdata or a database backup tool)
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f"backup_{timestamp}.zip"

        # Create a temporary file
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a zip file
            zip_path = os.path.join(temp_dir, backup_filename)
            with zipfile.ZipFile(zip_path, 'w') as backup_zip:
                # Add a dummy file for demonstration
                dummy_file_path = os.path.join(temp_dir, 'backup_info.txt')
                with open(dummy_file_path, 'w') as f:
                    f.write(f"Backup created on {datetime.datetime.now()}\n")
                    f.write(f"Description: {description}\n")

                backup_zip.write(dummy_file_path, 'backup_info.txt')

            # Get the file size
            file_size = os.path.getsize(zip_path)

            # Create the backup record
            backup = Backup.objects.create(
                name=name,
                description=description,
                created_by=request.user,
                file_size=file_size,
            )

            # Save the file to the backup record
            with open(zip_path, 'rb') as f:
                backup.backup_file.save(backup_filename, f)

        # Log the backup action
        AuditLog.objects.create(
            user=request.user,
            action='backup',
            description=_('تم إنشاء نسخة احتياطية'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إنشاء النسخة الاحتياطية بنجاح'))
        return redirect('dashboard:backups')

    return render(request, 'dashboard/create_backup.html')

@login_required
def restore_backup(request, backup_id):
    """Restore from a backup"""
    if not request.user.is_superuser:
        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة'))
        return redirect('dashboard:index')

    backup = get_object_or_404(Backup, id=backup_id)

    if request.method == 'POST':
        # This is a simplified version, in a real app you would restore the database from the backup file
        # Log the restore action
        AuditLog.objects.create(
            user=request.user,
            action='restore',
            description=_('تم استعادة النسخة الاحتياطية'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم استعادة النسخة الاحتياطية بنجاح'))
        return redirect('dashboard:backups')

    context = {
        'backup': backup,
    }

    return render(request, 'dashboard/restore_backup.html', context)

@login_required
def statistics(request):
    """View statistics and charts for legal department"""
    # Get current date and year start date
    today = timezone.now().date()
    year_start = datetime.date(today.year, 1, 1)

    # Get counts for different models
    guarantee_count = Guarantee.objects.count()
    active_guarantee_count = Guarantee.objects.filter(is_active=True).count()
    expired_guarantee_count = Guarantee.objects.filter(is_active=False).count()

    council_count = Council.objects.count()
    active_council_count = Council.objects.filter(is_closed=False).count()
    closed_council_count = Council.objects.filter(is_closed=True).count()
    expired_council_count = sum(1 for council in Council.objects.filter(is_closed=False) if council.is_expired())

    # القضايا القانونية غير مطلوبة في هذا النظام
    case_count = 0
    open_case_count = 0
    closed_case_count = 0
    pending_case_count = 0

    # Get monthly statistics for guarantees
    guarantees_by_month = Guarantee.objects.annotate(
        month=TruncMonth('issue_date')
    ).values('month').annotate(
        count=Count('id')
    ).order_by('month')

    # Get monthly statistics for councils
    councils_by_month = Council.objects.annotate(
        month=TruncMonth('formation_date')
    ).values('month').annotate(
        count=Count('id')
    ).order_by('month')

    # القضايا القانونية غير مطلوبة في هذا النظام
    case_types = []

    # Get council types distribution
    council_types = Council.objects.values('council_type').annotate(
        count=Count('id')
    ).order_by('council_type')

    # Get department statistics
    departments = Department.objects.all()
    department_stats = []

    for dept in departments:
        dept_stats = {
            'name': dept.name,
            'case_count': 0,  # القضايا القانونية غير مطلوبة في هذا النظام
            'open_cases': 0,
            'closed_cases': 0,
        }
        department_stats.append(dept_stats)

    # Prepare data for charts
    guarantees_chart_data = {
        'labels': [g['month'].strftime('%Y-%m') if g['month'] else 'غير محدد' for g in guarantees_by_month],
        'data': [g['count'] for g in guarantees_by_month],
    }

    councils_chart_data = {
        'labels': [c['month'].strftime('%Y-%m') if c['month'] else 'غير محدد' for c in councils_by_month],
        'data': [c['count'] for c in councils_by_month],
    }

    case_types_chart_data = {
        'labels': [],
        'data': [],
    }

    council_types_chart_data = {
        'labels': [dict(Council.COUNCIL_TYPES).get(ct['council_type'], 'غير محدد') for ct in council_types],
        'data': [ct['count'] for ct in council_types],
    }

    department_chart_data = {
        'labels': [dept['name'] for dept in department_stats],
        'open_cases': [dept['open_cases'] for dept in department_stats],
        'closed_cases': [dept['closed_cases'] for dept in department_stats],
    }

    context = {
        'guarantee_count': guarantee_count,
        'active_guarantee_count': active_guarantee_count,
        'expired_guarantee_count': expired_guarantee_count,
        'council_count': council_count,
        'active_council_count': active_council_count,
        'closed_council_count': closed_council_count,
        'expired_council_count': expired_council_count,
        'case_count': case_count,
        'open_case_count': open_case_count,
        'closed_case_count': closed_case_count,
        'pending_case_count': pending_case_count,
        'department_stats': department_stats,
        'guarantees_chart_data': json.dumps(guarantees_chart_data),
        'councils_chart_data': json.dumps(councils_chart_data),
        'case_types_chart_data': json.dumps(case_types_chart_data),
        'council_types_chart_data': json.dumps(council_types_chart_data),
        'department_chart_data': json.dumps(department_chart_data),
    }

    return render(request, 'dashboard/statistics.html', context)
