{% extends 'base/base.html' %}
{% load consultations_extras %}

{% block title %}حذف كفالة - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}حذ<PERSON> كفالة{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-3">
        <div class="col-md-12">
            <a href="{% url 'consultations:dynamic_guarantee_detail' guarantee.id %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i> العودة إلى تفاصيل الكفالة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 offset-md-3">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">تأكيد الحذف</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من رغبتك في حذف الكفالة؟
                    </div>

                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>تحذير:</strong> سيؤدي حذف الكفالة إلى حذف جميع البيانات والمستندات المرتبطة بها. هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم الكفيل:</label>
                        <p class="form-control-static">{{ guarantee|get_guarantee_field:'guarantor_name' }}</p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم المكفول:</label>
                        <p class="form-control-static">{{ guarantee|get_guarantee_field:'guaranteed_name' }}</p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">مبلغ الكفالة:</label>
                        <p class="form-control-static">{{ guarantee|get_guarantee_field:'guarantee_amount' }}</p>
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        <div class="text-center mt-4">
                            <a href="{% url 'consultations:dynamic_guarantee_detail' guarantee.id %}" class="btn btn-secondary me-2">إلغاء</a>
                            <button type="submit" class="btn btn-danger">تأكيد الحذف</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
