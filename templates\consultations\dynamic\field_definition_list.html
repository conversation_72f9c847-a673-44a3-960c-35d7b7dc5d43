{% extends 'base/base.html' %}

{% block title %}إدارة تعريفات الحقول - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}إدارة تعريفات الحقول{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{% url 'consultations:index' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i> العودة إلى الصفحة الرئيسية
                    </a>
                </div>
                <div>
                    <a href="{% url 'consultations:initialize_field_definitions' %}" class="btn btn-info me-2">
                        <i class="fas fa-sync me-2"></i> تهيئة الحقول الافتراضية
                    </a>
                    <a href="{% url 'consultations:field_definition_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> إضافة حقل جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">المعلومات الأساسية</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        هذه الحقول تظهر في قسم المعلومات الأساسية في نموذج الكفالة.
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الحقل</th>
                                    <th>مفتاح الحقل</th>
                                    <th>نوع الحقل</th>
                                    <th>مطلوب</th>
                                    <th>حقل نظام</th>
                                    <th>فعال</th>
                                    <th>ترتيب العرض</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for field in basic_fields %}
                                <tr>
                                    <td>{{ field.name }}</td>
                                    <td>{{ field.field_key }}</td>
                                    <td>{{ field.get_field_type_display }}</td>
                                    <td>
                                        {% if field.is_required %}
                                        <span class="badge bg-success">نعم</span>
                                        {% else %}
                                        <span class="badge bg-secondary">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if field.is_system %}
                                        <span class="badge bg-info">نعم</span>
                                        {% else %}
                                        <span class="badge bg-secondary">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if field.is_active %}
                                        <span class="badge bg-success">نعم</span>
                                        {% else %}
                                        <span class="badge bg-danger">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ field.display_order }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'consultations:field_definition_edit' field.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if not field.is_system %}
                                            <a href="{% url 'consultations:field_definition_delete' field.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد حقول في قسم المعلومات الأساسية</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معلومات إضافية</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        هذه الحقول تظهر في قسم المعلومات الإضافية في نموذج الكفالة.
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الحقل</th>
                                    <th>مفتاح الحقل</th>
                                    <th>نوع الحقل</th>
                                    <th>مطلوب</th>
                                    <th>حقل نظام</th>
                                    <th>فعال</th>
                                    <th>ترتيب العرض</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for field in additional_fields %}
                                <tr>
                                    <td>{{ field.name }}</td>
                                    <td>{{ field.field_key }}</td>
                                    <td>{{ field.get_field_type_display }}</td>
                                    <td>
                                        {% if field.is_required %}
                                        <span class="badge bg-success">نعم</span>
                                        {% else %}
                                        <span class="badge bg-secondary">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if field.is_system %}
                                        <span class="badge bg-info">نعم</span>
                                        {% else %}
                                        <span class="badge bg-secondary">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if field.is_active %}
                                        <span class="badge bg-success">نعم</span>
                                        {% else %}
                                        <span class="badge bg-danger">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ field.display_order }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'consultations:field_definition_edit' field.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if not field.is_system %}
                                            <a href="{% url 'consultations:field_definition_delete' field.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد حقول في قسم المعلومات الإضافية</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
