from django.db import models
from django.contrib.auth.models import User
from core.models import Department

class LegalCase(models.Model):
    """Model for legal cases"""
    CASE_STATUS = (
        ('open', 'مفتوحة'),
        ('closed', 'مغلقة'),
        ('pending', 'قيد الانتظار'),
    )

    CASE_TYPES = (
        ('consultation', 'استشارة قانونية'),
        ('lawsuit', 'دعوى قضائية'),
        ('administrative', 'قضية إدارية'),
        ('other', 'أخرى'),
    )

    title = models.CharField(max_length=255, verbose_name="عنوان القضية")
    case_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="رقم القضية")
    case_type = models.CharField(max_length=20, choices=CASE_TYPES, verbose_name="نوع القضية")
    status = models.CharField(max_length=20, choices=CASE_STATUS, default='open', verbose_name="حالة القضية")
    description = models.TextField(verbose_name="وصف القضية")
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, related_name='cases', verbose_name="القسم")
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_cases', verbose_name="مسند إلى")
    start_date = models.DateField(verbose_name="تاريخ البدء")
    end_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الانتهاء")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_cases', verbose_name="تم الإنشاء بواسطة")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='updated_cases', verbose_name="تم التحديث بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "قضية قانونية"
        verbose_name_plural = "القضايا القانونية"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.case_number or 'بدون رقم'}"

class LegalDocument(models.Model):
    """Model for legal documents"""
    case = models.ForeignKey(LegalCase, on_delete=models.CASCADE, related_name='documents', verbose_name="القضية")
    title = models.CharField(max_length=255, verbose_name="عنوان المستند")
    document_file = models.FileField(upload_to='legal_documents/', verbose_name="الملف")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='uploaded_legal_documents', verbose_name="تم الرفع بواسطة")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الرفع")

    class Meta:
        verbose_name = "مستند قانوني"
        verbose_name_plural = "المستندات القانونية"
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.title} - {self.case}"

class CaseNote(models.Model):
    """Model for case notes"""
    case = models.ForeignKey(LegalCase, on_delete=models.CASCADE, related_name='notes', verbose_name="القضية")
    note = models.TextField(verbose_name="الملاحظة")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='case_notes', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "ملاحظة قضية"
        verbose_name_plural = "ملاحظات القضايا"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.case} - {self.created_at}"
