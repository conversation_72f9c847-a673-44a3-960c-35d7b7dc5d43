# Generated by Django 5.2 on 2025-04-09 20:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Guarantee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('guarantor_name', models.CharField(max_length=255, verbose_name='اسم الكفيل')),
                ('guaranteed_name', models.CharField(max_length=255, verbose_name='اسم المكفول')),
                ('guarantee_duration', models.CharField(max_length=100, verbose_name='مدة الكفالة')),
                ('guarantee_amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='مبلغ الكفالة')),
                ('bank_name', models.Char<PERSON>ield(max_length=255, verbose_name='اسم المصرف المانح')),
                ('issue_date', models.DateField(verbose_name='تاريخ منح الكفالة')),
                ('expiry_date', models.DateField(verbose_name='تاريخ انتهاء الكفالة')),
                ('is_active', models.BooleanField(default=True, verbose_name='فعالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_guarantees', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_guarantees', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'كفالة',
                'verbose_name_plural': 'الكفالات',
                'ordering': ['-issue_date'],
            },
        ),
        migrations.CreateModel(
            name='GuaranteeDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('guarantee_letter', 'كتاب الكفالة'), ('expiry_letter', 'كتاب انتهاء الكفالة'), ('other', 'أخرى')], max_length=20, verbose_name='نوع المستند')),
                ('document_file', models.FileField(upload_to='guarantee_documents/', verbose_name='الملف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('guarantee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='consultations.guarantee', verbose_name='الكفالة')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_guarantee_documents', to=settings.AUTH_USER_MODEL, verbose_name='تم الرفع بواسطة')),
            ],
            options={
                'verbose_name': 'مستند الكفالة',
                'verbose_name_plural': 'مستندات الكفالة',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='GuaranteeField',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الحقل')),
                ('field_type', models.CharField(choices=[('text', 'نص'), ('number', 'رقم'), ('date', 'تاريخ'), ('boolean', 'نعم/لا')], max_length=20, verbose_name='نوع الحقل')),
                ('is_required', models.BooleanField(default=False, verbose_name='مطلوب')),
                ('is_active', models.BooleanField(default=True, verbose_name='فعال')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_guarantee_fields', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'حقل الكفالة',
                'verbose_name_plural': 'حقول الكفالة',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='GuaranteeFieldValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.TextField(verbose_name='القيمة')),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='values', to='consultations.guaranteefield', verbose_name='الحقل')),
                ('guarantee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='field_values', to='consultations.guarantee', verbose_name='الكفالة')),
            ],
            options={
                'verbose_name': 'قيمة حقل الكفالة',
                'verbose_name_plural': 'قيم حقول الكفالة',
                'unique_together': {('guarantee', 'field')},
            },
        ),
    ]
