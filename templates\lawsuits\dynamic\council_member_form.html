{% extends 'base/base.html' %}

{% block title %}
    {% if is_edit %}
    تعديل عضو المجلس/اللجنة - هيأة المنافذ الحدودية
    {% else %}
    إضافة عضو جديد للمجلس/اللجنة - هيأة المنافذ الحدودية
    {% endif %}
{% endblock %}

{% block page_title %}
    {% if is_edit %}
    تعديل عضو المجلس/اللجنة
    {% else %}
    إضافة عضو جديد للمجلس/اللجنة
    {% endif %}
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        {% if is_edit %}
                        تعديل عضو المجلس/اللجنة
                        {% else %}
                        إضافة عضو جديد للمجلس/اللجنة
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم العضو <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required 
                                {% if member %}value="{{ member.name }}"{% endif %}>
                        </div>
                        
                        <div class="mb-3">
                            <label for="position" class="form-label">مقر العمل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="position" name="position" required 
                                {% if member %}value="{{ member.position }}"{% endif %}>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_head" name="is_head" 
                                {% if member and member.is_head %}checked{% endif %}>
                            <label class="form-check-label" for="is_head">رئيس اللجنة</label>
                            <div class="form-text text-muted">
                                إذا تم تحديد هذا العضو كرئيس للجنة، سيتم إلغاء تحديد أي رئيس آخر.
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'lawsuits:dynamic_council_detail' council.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i> العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> 
                                {% if is_edit %}
                                حفظ التغييرات
                                {% else %}
                                إضافة العضو
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
