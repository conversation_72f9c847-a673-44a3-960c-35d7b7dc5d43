from django.db import models
from django.contrib.auth.models import User

class Guarantee(models.Model):
    """Model for guarantees in the consultations department"""
    guarantor_name = models.CharField(max_length=255, verbose_name="اسم الكفيل")
    guaranteed_name = models.CharField(max_length=255, verbose_name="اسم المكفول")
    guarantee_duration = models.CharField(max_length=100, verbose_name="مدة الكفالة")
    guarantee_amount = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="مبلغ الكفالة")
    bank_name = models.CharField(max_length=255, verbose_name="اسم المصرف المانح")
    issue_date = models.DateField(verbose_name="تاريخ منح الكفالة")
    expiry_date = models.DateField(verbose_name="تاريخ انتهاء الكفالة")
    is_active = models.BooleanField(default=True, verbose_name="فعالة")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_guarantees', verbose_name="تم الإنشاء بواسطة")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='updated_guarantees', verbose_name="تم التحديث بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "كفالة"
        verbose_name_plural = "الكفالات"
        ordering = ['-issue_date']

    def __str__(self):
        return f"{self.guarantor_name} - {self.guaranteed_name} - {self.guarantee_amount}"

class GuaranteeDocument(models.Model):
    """Model for guarantee documents"""
    DOCUMENT_TYPES = (
        ('guarantee_letter', 'كتاب الكفالة'),
        ('expiry_letter', 'كتاب انتهاء الكفالة'),
        ('other', 'أخرى'),
    )

    guarantee = models.ForeignKey(Guarantee, on_delete=models.CASCADE, related_name='documents', verbose_name="الكفالة")
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES, verbose_name="نوع المستند")
    document_file = models.FileField(upload_to='guarantee_documents/', verbose_name="الملف")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='uploaded_guarantee_documents', verbose_name="تم الرفع بواسطة")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الرفع")

    class Meta:
        verbose_name = "مستند الكفالة"
        verbose_name_plural = "مستندات الكفالة"
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.get_document_type_display()} - {self.guarantee}"

class GuaranteeField(models.Model):
    """Model for dynamic fields in the guarantee model"""
    FIELD_TYPES = (
        ('text', 'نص'),
        ('number', 'رقم'),
        ('date', 'تاريخ'),
        ('boolean', 'نعم/لا'),
    )

    name = models.CharField(max_length=100, verbose_name="اسم الحقل")
    field_type = models.CharField(max_length=20, choices=FIELD_TYPES, verbose_name="نوع الحقل")
    is_required = models.BooleanField(default=False, verbose_name="مطلوب")
    is_active = models.BooleanField(default=True, verbose_name="فعال")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_guarantee_fields', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "حقل الكفالة"
        verbose_name_plural = "حقول الكفالة"
        ordering = ['name']

    def __str__(self):
        return self.name

class GuaranteeFieldValue(models.Model):
    """Model for values of dynamic fields in guarantees"""
    guarantee = models.ForeignKey(Guarantee, on_delete=models.CASCADE, related_name='field_values', verbose_name="الكفالة")
    field = models.ForeignKey(GuaranteeField, on_delete=models.CASCADE, related_name='values', verbose_name="الحقل")
    value = models.TextField(verbose_name="القيمة")

    class Meta:
        verbose_name = "قيمة حقل الكفالة"
        verbose_name_plural = "قيم حقول الكفالة"
        unique_together = ('guarantee', 'field')

    def __str__(self):
        return f"{self.field.name}: {self.value}"
