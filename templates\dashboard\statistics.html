{% extends 'base/base.html' %}

{% block title %}إحصائيات الدائرة القانونية - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}إحصائيات الدائرة القانونية{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        transition: all 0.3s ease;
    }
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Statistics Summary Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">ملخص الإحصائيات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Guarantees Stats -->
                        <div class="col-md-4 mb-3">
                            <div class="card stats-card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">الكفالات</h5>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="me-2">
                                                    <span class="badge bg-primary rounded-circle p-2">
                                                        <i class="fas fa-file-contract"></i>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="small text-muted">الإجمالي</div>
                                                    <div class="fw-bold">{{ guarantee_count }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="me-2">
                                                    <span class="badge bg-success rounded-circle p-2">
                                                        <i class="fas fa-check-circle"></i>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="small text-muted">الفعالة</div>
                                                    <div class="fw-bold">{{ active_guarantee_count }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="d-flex align-items-center">
                                                <div class="me-2">
                                                    <span class="badge bg-danger rounded-circle p-2">
                                                        <i class="fas fa-times-circle"></i>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="small text-muted">المنتهية</div>
                                                    <div class="fw-bold">{{ expired_guarantee_count }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Councils Stats -->
                        <div class="col-md-4 mb-3">
                            <div class="card stats-card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">المجالس/اللجان</h5>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="me-2">
                                                    <span class="badge bg-primary rounded-circle p-2">
                                                        <i class="fas fa-gavel"></i>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="small text-muted">الإجمالي</div>
                                                    <div class="fw-bold">{{ council_count }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="me-2">
                                                    <span class="badge bg-success rounded-circle p-2">
                                                        <i class="fas fa-clipboard-check"></i>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="small text-muted">الفعالة</div>
                                                    <div class="fw-bold">{{ active_council_count }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="d-flex align-items-center">
                                                <div class="me-2">
                                                    <span class="badge bg-secondary rounded-circle p-2">
                                                        <i class="fas fa-folder-closed"></i>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="small text-muted">المغلقة</div>
                                                    <div class="fw-bold">{{ closed_council_count }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="d-flex align-items-center">
                                                <div class="me-2">
                                                    <span class="badge bg-danger rounded-circle p-2">
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="small text-muted">المتجاوزة للمدة</div>
                                                    <div class="fw-bold">{{ expired_council_count }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تم إزالة قسم القضايا القانونية لأنه غير مطلوب في هذا النظام -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="row mb-4">
        <!-- Monthly Guarantees Chart -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">الكفالات الشهرية</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="guaranteesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Councils Chart -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">المجالس/اللجان الشهرية</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="councilsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row mb-4">
        <!-- Council Types Chart -->
        <div class="col-md-12 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">توزيع أنواع المجالس/اللجان</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="councilTypesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">إحصائيات الأقسام</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="departmentChart"></canvas>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        إحصائيات الأقسام غير متوفرة حاليًا لأن النظام لا يتضمن قسم القضايا القانونية.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set Chart.js default options
        Chart.defaults.font.family = "'Tajawal', sans-serif";
        Chart.defaults.font.size = 14;
        Chart.defaults.color = '#666';
        Chart.defaults.plugins.legend.position = 'bottom';
        Chart.defaults.plugins.tooltip.padding = 10;
        Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        Chart.defaults.plugins.tooltip.titleFont = { size: 14 };
        Chart.defaults.plugins.tooltip.bodyFont = { size: 13 };
        Chart.defaults.plugins.tooltip.displayColors = true;
        Chart.defaults.plugins.tooltip.boxPadding = 5;

        // Guarantees Chart
        const guaranteesData = {{ guarantees_chart_data|safe }};
        const guaranteesCtx = document.getElementById('guaranteesChart').getContext('2d');
        new Chart(guaranteesCtx, {
            type: 'line',
            data: {
                labels: guaranteesData.labels,
                datasets: [{
                    label: 'عدد الكفالات',
                    data: guaranteesData.data,
                    backgroundColor: 'rgba(13, 110, 253, 0.2)',
                    borderColor: 'rgba(13, 110, 253, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true,
                    pointBackgroundColor: 'rgba(13, 110, 253, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 7
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    title: {
                        display: false,
                        text: 'الكفالات الشهرية'
                    }
                }
            }
        });

        // Councils Chart
        const councilsData = {{ councils_chart_data|safe }};
        const councilsCtx = document.getElementById('councilsChart').getContext('2d');
        new Chart(councilsCtx, {
            type: 'line',
            data: {
                labels: councilsData.labels,
                datasets: [{
                    label: 'عدد المجالس/اللجان',
                    data: councilsData.data,
                    backgroundColor: 'rgba(25, 135, 84, 0.2)',
                    borderColor: 'rgba(25, 135, 84, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true,
                    pointBackgroundColor: 'rgba(25, 135, 84, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 7
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    title: {
                        display: false,
                        text: 'المجالس/اللجان الشهرية'
                    }
                }
            }
        });

        // تم إزالة رسم توزيع أنواع القضايا لأنه غير مطلوب في هذا النظام

        // Council Types Chart
        const councilTypesData = {{ council_types_chart_data|safe }};
        const councilTypesCtx = document.getElementById('councilTypesChart').getContext('2d');
        new Chart(councilTypesCtx, {
            type: 'pie',
            data: {
                labels: councilTypesData.labels,
                datasets: [{
                    data: councilTypesData.data,
                    backgroundColor: [
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(25, 135, 84, 0.7)',
                        'rgba(220, 53, 69, 0.7)'
                    ],
                    borderColor: [
                        'rgba(13, 110, 253, 1)',
                        'rgba(25, 135, 84, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false,
                        text: 'توزيع أنواع المجالس/اللجان'
                    }
                }
            }
        });

        // تم إزالة رسم إحصائيات الأقسام لأنه غير مطلوب في هذا النظام
    });
</script>
{% endblock %}
