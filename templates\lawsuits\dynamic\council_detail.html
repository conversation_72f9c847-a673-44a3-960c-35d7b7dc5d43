{% extends 'base/base.html' %}
{% load lawsuits_extras %}

{% block title %}تفاصيل المجلس/اللجنة{% endblock %}

{% block page_title %}تفاصيل المجلس/اللجنة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        {{ council|get_council_field:'council_type' }} - {{ council|get_council_field:'formation_order' }}
                    </h5>
                    <div>
                        <a href="{% url 'lawsuits:dynamic_council_edit' council.id %}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> تعديل
                        </a>
                        <a href="{% url 'lawsuits:dynamic_council_list' %}" class="btn btn-light btn-sm ms-2">
                            <i class="fas fa-list"></i> قائمة المجالس/اللجان
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">معلومات المجلس/اللجنة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        {% for field in basic_fields %}
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body">
                                                    <h6 class="card-subtitle mb-2 text-muted">{{ field.name }}</h6>
                                                    <p class="card-text">{{ council|get_council_field:field.field_key }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if additional_fields %}
                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">معلومات إضافية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        {% for field in additional_fields %}
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body">
                                                    <h6 class="card-subtitle mb-2 text-muted">{{ field.name }}</h6>
                                                    <p class="card-text">{{ council|get_council_field:field.field_key }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <div class="col-md-12 mb-4">
                            <div class="card">
                                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">أعضاء المجلس/اللجنة</h6>
                                    <a href="{% url 'lawsuits:add_dynamic_council_member' council.id %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus-circle"></i> إضافة عضو
                                    </a>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>الاسم</th>
                                                    <th>المنصب</th>
                                                    <th>رئيس اللجنة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for member in council.members.all %}
                                                <tr>
                                                    <td>{{ forloop.counter }}</td>
                                                    <td>{{ member.name }}</td>
                                                    <td>{{ member.position }}</td>
                                                    <td>
                                                        {% if member.is_head %}
                                                            <span class="badge bg-success">نعم</span>
                                                        {% else %}
                                                            <span class="badge bg-secondary">لا</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="{% url 'lawsuits:edit_dynamic_council_member' council.id member.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="{% url 'lawsuits:delete_dynamic_council_member' council.id member.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% empty %}
                                                <tr>
                                                    <td colspan="5" class="text-center">لا يوجد أعضاء</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">مستندات المجلس/اللجنة</h6>
                                    <a href="{% url 'lawsuits:upload_dynamic_council_document' council.id %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus-circle"></i> إضافة مستند
                                    </a>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>نوع المستند</th>
                                                    <th>الوصف</th>
                                                    <th>تاريخ الرفع</th>
                                                    <th>تم الرفع بواسطة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for document in council.documents.all %}
                                                <tr>
                                                    <td>{{ forloop.counter }}</td>
                                                    <td>{{ document.get_document_type_display }}</td>
                                                    <td>{{ document.description }}</td>
                                                    <td>{{ document.uploaded_at|date:"Y-m-d" }}</td>
                                                    <td>{{ document.uploaded_by.get_full_name }}</td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="{{ document.document_file.url }}" class="btn btn-sm btn-info" target="_blank" data-bs-toggle="tooltip" title="عرض">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="#" class="btn btn-sm btn-warning disabled" data-bs-toggle="tooltip" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="#" class="btn btn-sm btn-danger disabled" data-bs-toggle="tooltip" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                                {% empty %}
                                                <tr>
                                                    <td colspan="6" class="text-center">لا توجد مستندات</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
