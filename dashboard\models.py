from django.db import models
from django.contrib.auth.models import User

class Notification(models.Model):
    """Model for system notifications"""
    NOTIFICATION_TYPES = (
        ('info', 'معلومات'),
        ('warning', 'تحذير'),
        ('danger', 'خطر'),
        ('success', 'نجاح'),
    )

    title = models.CharField(max_length=255, verbose_name="العنوان")
    message = models.TextField(verbose_name="الرسالة")
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, default='info', verbose_name="نوع الإشعار")
    link = models.CharField(max_length=255, blank=True, null=True, verbose_name="الرابط")
    is_read = models.BooleanField(default=False, verbose_name="تمت القراءة")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications', verbose_name="المستخدم")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "إشعار"
        verbose_name_plural = "الإشعارات"
        ordering = ['-created_at']

    def __str__(self):
        return self.title

class Report(models.Model):
    """Model for saved reports"""
    REPORT_TYPES = (
        ('guarantee', 'تقرير الكفالات'),
        ('council', 'تقرير المجالس/اللجان'),
        ('custom', 'تقرير مخصص'),
    )

    name = models.CharField(max_length=255, verbose_name="اسم التقرير")
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES, verbose_name="نوع التقرير")
    query_params = models.JSONField(verbose_name="معايير البحث")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reports', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "تقرير"
        verbose_name_plural = "التقارير"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.get_report_type_display()}"

class Backup(models.Model):
    """Model for system backups"""
    name = models.CharField(max_length=255, verbose_name="اسم النسخة الاحتياطية")
    backup_file = models.FileField(upload_to='backups/', verbose_name="ملف النسخة الاحتياطية")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='backups', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    file_size = models.PositiveIntegerField(default=0, verbose_name="حجم الملف (بالبايت)")

    class Meta:
        verbose_name = "نسخة احتياطية"
        verbose_name_plural = "النسخ الاحتياطية"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.created_at}"
