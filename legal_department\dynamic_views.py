from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext as _
from django.db.models import Q
from django.core.paginator import Paginator
from django.utils import timezone

from .dynamic_models import FieldDefinition, DynamicLegalCase, FieldValue, DynamicLegalDocument, DynamicCaseNote
from core.models import AuditLog, Department

@login_required
def dynamic_index(request):
    """Dynamic legal cases home page"""
    # Get counts
    case_count = DynamicLegalCase.objects.count()
    open_case_count = DynamicLegalCase.objects.filter(status='open').count()
    pending_case_count = DynamicLegalCase.objects.filter(status='pending').count()
    closed_case_count = DynamicLegalCase.objects.filter(status='closed').count()

    # Get recent cases
    recent_cases = DynamicLegalCase.objects.all().order_by('-created_at')[:5]

    context = {
        'case_count': case_count,
        'open_case_count': open_case_count,
        'pending_case_count': pending_case_count,
        'closed_case_count': closed_case_count,
        'recent_cases': recent_cases,
    }

    return render(request, 'legal_department/dynamic/index.html', context)

@login_required
def field_definition_list(request):
    """List all field definitions"""
    # Check if user is staff
    if not request.user.is_staff:
        messages.error(request, _('ليس لديك صلاحية الوصول إلى هذه الصفحة'))
        return redirect('legal_department:index')

    # Get all field definitions
    fields = FieldDefinition.objects.all().order_by('section', 'display_order', 'name')

    context = {
        'fields': fields,
    }

    return render(request, 'legal_department/dynamic/field_definition_list.html', context)

@login_required
def field_definition_create(request):
    """Create a new field definition"""
    # Check if user is staff
    if not request.user.is_staff:
        messages.error(request, _('ليس لديك صلاحية الوصول إلى هذه الصفحة'))
        return redirect('legal_department:index')

    if request.method == 'POST':
        # Get form data
        name = request.POST.get('name')
        field_key = request.POST.get('field_key')
        field_type = request.POST.get('field_type')
        section = request.POST.get('section')
        description = request.POST.get('description')
        options = request.POST.get('options')
        is_required = request.POST.get('is_required') == 'on'
        is_system = request.POST.get('is_system') == 'on'
        display_order = int(request.POST.get('display_order', 0))

        # Create field definition
        field = FieldDefinition.objects.create(
            name=name,
            field_key=field_key,
            field_type=field_type,
            section=section,
            description=description,
            options=options,
            is_required=is_required,
            is_system=is_system,
            is_active=True,
            display_order=display_order,
            created_by=request.user,
        )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='FieldDefinition',
            object_id=field.id,
            description=_('تم إنشاء تعريف حقل جديد'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إنشاء تعريف الحقل بنجاح'))
        return redirect('legal_department:field_definition_list')

    context = {
        'field_types': FieldDefinition.FIELD_TYPES,
        'sections': FieldDefinition.SECTIONS,
    }

    return render(request, 'legal_department/dynamic/field_definition_form.html', context)

@login_required
def field_definition_edit(request, field_id):
    """Edit an existing field definition"""
    # Check if user is staff
    if not request.user.is_staff:
        messages.error(request, _('ليس لديك صلاحية الوصول إلى هذه الصفحة'))
        return redirect('legal_department:index')

    field = get_object_or_404(FieldDefinition, id=field_id)

    if request.method == 'POST':
        # Get form data
        field.name = request.POST.get('name')
        field.field_key = request.POST.get('field_key')
        field.field_type = request.POST.get('field_type')
        field.section = request.POST.get('section')
        field.description = request.POST.get('description')
        field.options = request.POST.get('options')
        field.is_required = request.POST.get('is_required') == 'on'
        field.is_system = request.POST.get('is_system') == 'on'
        field.is_active = request.POST.get('is_active') == 'on'
        field.display_order = int(request.POST.get('display_order', 0))
        field.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='FieldDefinition',
            object_id=field.id,
            description=_('تم تحديث تعريف الحقل'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث تعريف الحقل بنجاح'))
        return redirect('legal_department:field_definition_list')

    context = {
        'field': field,
        'field_types': FieldDefinition.FIELD_TYPES,
        'sections': FieldDefinition.SECTIONS,
        'is_edit': True,
    }

    return render(request, 'legal_department/dynamic/field_definition_form.html', context)

@login_required
def field_definition_delete(request, field_id):
    """Delete a field definition"""
    # Check if user is staff
    if not request.user.is_staff:
        messages.error(request, _('ليس لديك صلاحية الوصول إلى هذه الصفحة'))
        return redirect('legal_department:index')

    field = get_object_or_404(FieldDefinition, id=field_id)

    # Check if field is used in any case
    if FieldValue.objects.filter(field=field).exists():
        messages.error(request, _('لا يمكن حذف هذا الحقل لأنه مستخدم في دعاوى'))
        return redirect('legal_department:field_definition_list')

    if request.method == 'POST':
        # Log the action before deletion
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            model_name='FieldDefinition',
            object_id=field.id,
            description=_('تم حذف تعريف الحقل'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        field.delete()
        messages.success(request, _('تم حذف تعريف الحقل بنجاح'))
        return redirect('legal_department:field_definition_list')

    context = {
        'field': field,
    }

    return render(request, 'legal_department/dynamic/field_definition_confirm_delete.html', context)

@login_required
def initialize_case_fields(request):
    """Initialize default field definitions for legal cases"""
    # Check if user is staff
    if not request.user.is_staff:
        messages.error(request, _('ليس لديك صلاحية الوصول إلى هذه الصفحة'))
        return redirect('legal_department:index')

    # Define default fields
    default_fields = [
        # Basic information fields
        {
            'name': 'عنوان القضية',
            'field_key': 'title',
            'field_type': 'text',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 1,
        },
        {
            'name': 'رقم القضية',
            'field_key': 'case_number',
            'field_type': 'text',
            'section': 'basic',
            'is_required': False,
            'is_system': True,
            'display_order': 2,
        },
        {
            'name': 'نوع القضية',
            'field_key': 'case_type',
            'field_type': 'select',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 3,
            'options': 'استشارة قانونية,دعوى قضائية,قضية إدارية,أخرى',
        },
        {
            'name': 'وصف القضية',
            'field_key': 'description',
            'field_type': 'text',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 4,
        },
        {
            'name': 'تاريخ البدء',
            'field_key': 'start_date',
            'field_type': 'date',
            'section': 'basic',
            'is_required': True,
            'is_system': True,
            'display_order': 5,
        },

        # Additional information fields
        {
            'name': 'تاريخ الانتهاء',
            'field_key': 'end_date',
            'field_type': 'date',
            'section': 'additional',
            'is_required': False,
            'is_system': True,
            'display_order': 1,
        },
        {
            'name': 'الجهة المقابلة',
            'field_key': 'opposing_party',
            'field_type': 'text',
            'section': 'additional',
            'is_required': False,
            'is_system': True,
            'display_order': 2,
        },
        {
            'name': 'المحكمة',
            'field_key': 'court',
            'field_type': 'text',
            'section': 'additional',
            'is_required': False,
            'is_system': True,
            'display_order': 3,
        },
    ]

    # Create fields if they don't exist
    created_count = 0
    for field_data in default_fields:
        field_key = field_data.pop('field_key')
        options = field_data.pop('options', None)

        # Check if field already exists
        if not FieldDefinition.objects.filter(field_key=field_key).exists():
            # Create field
            FieldDefinition.objects.create(
                field_key=field_key,
                options=options,
                created_by=request.user,
                **field_data
            )
            created_count += 1

    if created_count > 0:
        messages.success(request, _(f'تم إنشاء {created_count} حقل افتراضي بنجاح'))
    else:
        messages.info(request, _('جميع الحقول الافتراضية موجودة بالفعل'))

    return redirect('legal_department:field_definition_list')

@login_required
def dynamic_case_list(request):
    """List all dynamic legal cases"""
    # Get filter parameters
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', 'all')
    case_type_filter = request.GET.get('case_type', '')
    department_filter = request.GET.get('department', '')
    start_date = request.GET.get('start_date', '')
    end_date = request.GET.get('end_date', '')

    # Base queryset
    cases = DynamicLegalCase.objects.all()

    # Apply filters
    if search_query:
        # Get field definitions for title, case number, and description
        try:
            title_field = FieldDefinition.objects.get(field_key='title')
            case_number_field = FieldDefinition.objects.get(field_key='case_number')
            description_field = FieldDefinition.objects.get(field_key='description')

            # Get cases with matching field values
            title_values = FieldValue.objects.filter(field=title_field, value__icontains=search_query).values_list('case_id', flat=True)
            case_number_values = FieldValue.objects.filter(field=case_number_field, value__icontains=search_query).values_list('case_id', flat=True)
            description_values = FieldValue.objects.filter(field=description_field, value__icontains=search_query).values_list('case_id', flat=True)

            # Combine the results
            cases = cases.filter(Q(id__in=title_values) | Q(id__in=case_number_values) | Q(id__in=description_values))
        except FieldDefinition.DoesNotExist:
            # If field definitions don't exist, return empty queryset
            cases = DynamicLegalCase.objects.none()

    if status_filter != 'all':
        cases = cases.filter(status=status_filter)

    if case_type_filter:
        # Get field definition for case type
        try:
            case_type_field = FieldDefinition.objects.get(field_key='case_type')
            case_type_values = FieldValue.objects.filter(field=case_type_field, value=case_type_filter).values_list('case_id', flat=True)
            cases = cases.filter(id__in=case_type_values)
        except FieldDefinition.DoesNotExist:
            pass

    if department_filter:
        cases = cases.filter(department_id=department_filter)

    if start_date:
        # Get field definition for start date
        try:
            start_date_field = FieldDefinition.objects.get(field_key='start_date')
            start_date_values = FieldValue.objects.filter(field=start_date_field, value__gte=start_date).values_list('case_id', flat=True)
            cases = cases.filter(id__in=start_date_values)
        except FieldDefinition.DoesNotExist:
            pass

    if end_date:
        # Get field definition for end date
        try:
            end_date_field = FieldDefinition.objects.get(field_key='end_date')
            end_date_values = FieldValue.objects.filter(field=end_date_field, value__lte=end_date).values_list('case_id', flat=True)
            cases = cases.filter(id__in=end_date_values)
        except FieldDefinition.DoesNotExist:
            pass

    # Order by
    cases = cases.order_by('-created_at')

    # Pagination
    paginator = Paginator(cases, 10)  # Show 10 cases per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get case types for filter
    try:
        case_type_field = FieldDefinition.objects.get(field_key='case_type')
        case_types = case_type_field.get_options_list()
    except FieldDefinition.DoesNotExist:
        case_types = []

    # Get departments for filter
    departments = Department.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'case_type_filter': case_type_filter,
        'department_filter': department_filter,
        'start_date': start_date,
        'end_date': end_date,
        'case_types': case_types,
        'departments': departments,
        'case_statuses': DynamicLegalCase.CASE_STATUS,
    }

    return render(request, 'legal_department/dynamic/case_list.html', context)

@login_required
def dynamic_case_create(request):
    """Create a new dynamic legal case"""
    # Get all active field definitions
    fields = FieldDefinition.objects.filter(is_active=True).order_by('section', 'display_order', 'name')

    if request.method == 'POST':
        # Get basic case data
        status = request.POST.get('status')
        department_id = request.POST.get('department')
        assigned_to_id = request.POST.get('assigned_to')

        # Create case
        case = DynamicLegalCase.objects.create(
            status=status,
            department_id=department_id if department_id else None,
            assigned_to_id=assigned_to_id if assigned_to_id else None,
            created_by=request.user,
            updated_by=request.user,
        )

        # Process field values
        for field in fields:
            field_key = f'field_{field.field_key}'

            if field.field_type == 'boolean':
                # For boolean fields, we only get a value if the checkbox is checked
                if field_key in request.POST:
                    value = 'true'
                else:
                    value = 'false'

                FieldValue.objects.create(
                    case=case,
                    field=field,
                    value=value
                )

            elif field.field_type == 'multi_select':
                # For multi-select fields, we get a list of values
                values = request.POST.getlist(field_key)
                if values:
                    # Store as comma-separated string
                    FieldValue.objects.create(
                        case=case,
                        field=field,
                        value=','.join(values)
                    )

            else:
                # For all other fields
                field_value = request.POST.get(field_key)
                if field_value:
                    FieldValue.objects.create(
                        case=case,
                        field=field,
                        value=field_value
                    )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='DynamicLegalCase',
            object_id=case.id,
            description=_('تم إنشاء قضية جديدة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إنشاء القضية بنجاح'))
        return redirect('legal_department:dynamic_case_detail', case_id=case.id)

    # Group fields by section
    basic_fields = fields.filter(section='basic')
    additional_fields = fields.filter(section='additional')

    # Get departments and users for assignment
    departments = Department.objects.filter(is_active=True)

    context = {
        'basic_fields': basic_fields,
        'additional_fields': additional_fields,
        'departments': departments,
        'case_statuses': DynamicLegalCase.CASE_STATUS,
        'is_edit': False,
    }

    return render(request, 'legal_department/dynamic/case_form.html', context)

@login_required
def dynamic_case_detail(request, case_id):
    """Display dynamic legal case details"""
    case = get_object_or_404(DynamicLegalCase, id=case_id)
    documents = case.documents.all()
    notes = case.notes.all().order_by('-created_at')
    field_values = case.field_values.all().select_related('field')

    # Group field values by section
    basic_field_values = [fv for fv in field_values if fv.field.section == 'basic']
    additional_field_values = [fv for fv in field_values if fv.field.section == 'additional']

    context = {
        'case': case,
        'documents': documents,
        'notes': notes,
        'basic_field_values': basic_field_values,
        'additional_field_values': additional_field_values,
    }

    return render(request, 'legal_department/dynamic/case_detail.html', context)

@login_required
def dynamic_case_edit(request, case_id):
    """Edit an existing dynamic legal case"""
    case = get_object_or_404(DynamicLegalCase, id=case_id)
    fields = FieldDefinition.objects.filter(is_active=True).order_by('section', 'display_order', 'name')
    field_values = {fv.field.field_key: fv.value for fv in case.field_values.all().select_related('field')}

    if request.method == 'POST':
        # Update case
        case.status = request.POST.get('status')
        case.department_id = request.POST.get('department') or None
        case.assigned_to_id = request.POST.get('assigned_to') or None
        case.updated_by = request.user
        case.save()

        # Update field values
        for field in fields:
            field_key = f'field_{field.field_key}'

            if field.field_type == 'boolean':
                # For boolean fields, we only get a value if the checkbox is checked
                if field_key in request.POST:
                    value = 'true'
                else:
                    value = 'false'

                FieldValue.objects.update_or_create(
                    case=case,
                    field=field,
                    defaults={'value': value}
                )

            elif field.field_type == 'multi_select':
                # For multi-select fields, we get a list of values
                values = request.POST.getlist(field_key)
                if values:
                    # Store as comma-separated string
                    FieldValue.objects.update_or_create(
                        case=case,
                        field=field,
                        defaults={'value': ','.join(values)}
                    )

            else:
                # For all other fields
                field_value = request.POST.get(field_key)
                if field_value:
                    FieldValue.objects.update_or_create(
                        case=case,
                        field=field,
                        defaults={'value': field_value}
                    )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='DynamicLegalCase',
            object_id=case.id,
            description=_('تم تحديث القضية'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث القضية بنجاح'))
        return redirect('legal_department:dynamic_case_detail', case_id=case.id)

    # Group fields by section
    basic_fields = fields.filter(section='basic')
    additional_fields = fields.filter(section='additional')

    # Get departments and users for assignment
    departments = Department.objects.filter(is_active=True)

    context = {
        'case': case,
        'basic_fields': basic_fields,
        'additional_fields': additional_fields,
        'field_values': field_values,
        'departments': departments,
        'case_statuses': DynamicLegalCase.CASE_STATUS,
        'is_edit': True,
    }

    return render(request, 'legal_department/dynamic/case_form.html', context)

@login_required
def dynamic_case_delete(request, case_id):
    """Delete a dynamic legal case"""
    case = get_object_or_404(DynamicLegalCase, id=case_id)

    if request.method == 'POST':
        # Log the action before deletion
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            model_name='DynamicLegalCase',
            object_id=case.id,
            description=_('تم حذف القضية'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        case.delete()
        messages.success(request, _('تم حذف القضية بنجاح'))
        return redirect('legal_department:dynamic_case_list')

    context = {
        'case': case,
    }

    return render(request, 'legal_department/dynamic/case_confirm_delete.html', context)

@login_required
def dynamic_case_notes(request, case_id):
    """View dynamic case notes"""
    case = get_object_or_404(DynamicLegalCase, id=case_id)
    notes = case.notes.all().order_by('-created_at')

    context = {
        'case': case,
        'notes': notes,
    }

    return render(request, 'legal_department/dynamic/case_notes.html', context)

@login_required
def add_dynamic_case_note(request, case_id):
    """Add a note to a dynamic case"""
    case = get_object_or_404(DynamicLegalCase, id=case_id)

    if request.method == 'POST':
        note_text = request.POST.get('note')

        if note_text:
            # Create note
            note = DynamicCaseNote.objects.create(
                case=case,
                note=note_text,
                created_by=request.user
            )

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='create',
                model_name='DynamicCaseNote',
                object_id=note.id,
                description=_('تم إضافة ملاحظة جديدة للقضية'),
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, _('تم إضافة الملاحظة بنجاح'))
        else:
            messages.error(request, _('يرجى إدخال نص الملاحظة'))

        return redirect('legal_department:dynamic_case_notes', case_id=case.id)

    context = {
        'case': case,
    }

    return render(request, 'legal_department/dynamic/case_note_form.html', context)

@login_required
def upload_dynamic_case_document(request, case_id):
    """Upload a document for a dynamic case"""
    case = get_object_or_404(DynamicLegalCase, id=case_id)

    if request.method == 'POST':
        title = request.POST.get('title')
        description = request.POST.get('description')
        document_file = request.FILES.get('document_file')

        if document_file and title:
            document = DynamicLegalDocument.objects.create(
                case=case,
                title=title,
                document_file=document_file,
                description=description,
                uploaded_by=request.user
            )

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='create',
                model_name='DynamicLegalDocument',
                object_id=document.id,
                description=_('تم رفع مستند جديد للقضية'),
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, _('تم رفع المستند بنجاح'))
        else:
            if not title:
                messages.error(request, _('يرجى إدخال عنوان المستند'))
            if not document_file:
                messages.error(request, _('يرجى اختيار ملف لرفعه'))

        return redirect('legal_department:dynamic_case_detail', case_id=case.id)

    context = {
        'case': case,
    }

    return render(request, 'legal_department/dynamic/upload_document.html', context)