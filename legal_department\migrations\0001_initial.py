# Generated by Django 5.2 on 2025-04-09 20:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LegalCase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='عنوان القضية')),
                ('case_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم القضية')),
                ('case_type', models.CharField(choices=[('consultation', 'استشارة قانونية'), ('lawsuit', 'دعوى قضائية'), ('administrative', 'قضية إدارية'), ('other', 'أخرى')], max_length=20, verbose_name='نوع القضية')),
                ('status', models.CharField(choices=[('open', 'مفتوحة'), ('closed', 'مغلقة'), ('pending', 'قيد الانتظار')], default='open', max_length=20, verbose_name='حالة القضية')),
                ('description', models.TextField(verbose_name='وصف القضية')),
                ('start_date', models.DateField(verbose_name='تاريخ البدء')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_cases', to=settings.AUTH_USER_MODEL, verbose_name='مسند إلى')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_cases', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('department', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cases', to='core.department', verbose_name='القسم')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_cases', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'قضية قانونية',
                'verbose_name_plural': 'القضايا القانونية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CaseNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.TextField(verbose_name='الملاحظة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='case_notes', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='legal_department.legalcase', verbose_name='القضية')),
            ],
            options={
                'verbose_name': 'ملاحظة قضية',
                'verbose_name_plural': 'ملاحظات القضايا',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LegalDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='عنوان المستند')),
                ('document_file', models.FileField(upload_to='legal_documents/', verbose_name='الملف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='legal_department.legalcase', verbose_name='القضية')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_legal_documents', to=settings.AUTH_USER_MODEL, verbose_name='تم الرفع بواسطة')),
            ],
            options={
                'verbose_name': 'مستند قانوني',
                'verbose_name_plural': 'المستندات القانونية',
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
