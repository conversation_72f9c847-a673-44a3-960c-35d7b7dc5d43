{% extends 'base/base.html' %}

{% block title %}إدارة تعريفات الحقول - الدعاوى القانونية{% endblock %}

{% block page_title %}إدارة تعريفات الحقول - الدعاوى القانونية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">تعريفات الحقول</h5>
                    <div>
                        <a href="{% url 'legal_department:field_definition_create' %}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus-circle"></i> إضافة حقل جديد
                        </a>
                        <a href="{% url 'legal_department:initialize_case_fields' %}" class="btn btn-warning btn-sm ms-2">
                            <i class="fas fa-sync"></i> تهيئة الحقول الافتراضية
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم الحقل</th>
                                    <th>مفتاح الحقل</th>
                                    <th>نوع الحقل</th>
                                    <th>القسم</th>
                                    <th>مطلوب</th>
                                    <th>حقل نظام</th>
                                    <th>فعال</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for field in fields %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ field.name }}</td>
                                    <td>{{ field.field_key }}</td>
                                    <td>{{ field.get_field_type_display }}</td>
                                    <td>{{ field.get_section_display }}</td>
                                    <td>
                                        {% if field.is_required %}
                                            <span class="badge bg-success">نعم</span>
                                        {% else %}
                                            <span class="badge bg-secondary">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if field.is_system %}
                                            <span class="badge bg-info">نعم</span>
                                        {% else %}
                                            <span class="badge bg-secondary">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if field.is_active %}
                                            <span class="badge bg-success">نعم</span>
                                        {% else %}
                                            <span class="badge bg-danger">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'legal_department:field_definition_edit' field.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'legal_department:field_definition_delete' field.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center">لا توجد تعريفات حقول</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
