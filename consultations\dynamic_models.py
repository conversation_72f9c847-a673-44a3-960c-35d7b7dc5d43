from django.db import models
from django.contrib.auth.models import User

class FieldDefinition(models.Model):
    """Model for defining fields in the system"""
    FIELD_TYPES = (
        ('text', 'نص'),
        ('number', 'رقم'),
        ('date', 'تاريخ'),
        ('boolean', 'نعم/لا'),
        ('select', 'قائمة منسدلة'),
        ('multi_select', 'اختيار متعدد'),
    )

    SECTIONS = (
        ('basic', 'المعلومات الأساسية'),
        ('additional', 'معلومات إضافية'),
    )

    name = models.CharField(max_length=100, verbose_name="اسم الحقل")
    field_key = models.CharField(max_length=100, unique=True, verbose_name="مفتاح الحقل")
    field_type = models.CharField(max_length=20, choices=FIELD_TYPES, verbose_name="نوع الحقل")
    is_required = models.BooleanField(default=False, verbose_name="مطلوب")
    is_system = models.BooleanField(default=False, verbose_name="حقل نظام")
    is_active = models.BooleanField(default=True, verbose_name="فعال")
    display_order = models.IntegerField(default=0, verbose_name="ترتيب العرض")
    section = models.CharField(max_length=50, choices=SECTIONS, default='basic', verbose_name="القسم")
    options = models.TextField(blank=True, null=True, verbose_name="خيارات", help_text="للقوائم المنسدلة والاختيار المتعدد. افصل بين الخيارات بسطر جديد.")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_field_definitions', verbose_name="تم الإنشاء بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='updated_field_definitions', verbose_name="تم التحديث بواسطة")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "تعريف الحقل"
        verbose_name_plural = "تعريفات الحقول"
        ordering = ['display_order', 'name']

    def __str__(self):
        return self.name

class DynamicGuarantee(models.Model):
    """Dynamic model for guarantees"""
    is_active = models.BooleanField(default=True, verbose_name="فعالة")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_dynamic_guarantees', verbose_name="تم الإنشاء بواسطة")
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='updated_dynamic_guarantees', verbose_name="تم التحديث بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "كفالة ديناميكية"
        verbose_name_plural = "الكفالات الديناميكية"
        ordering = ['-created_at']

    def __str__(self):
        # Try to get guarantor and guaranteed names from field values
        guarantor_name = self.get_field_value('guarantor_name')
        guaranteed_name = self.get_field_value('guaranteed_name')
        guarantee_amount = self.get_field_value('guarantee_amount')

        if guarantor_name and guaranteed_name:
            return f"{guarantor_name} - {guaranteed_name} - {guarantee_amount or 'بدون مبلغ'}"
        return f"كفالة #{self.id}"

    def get_field_value(self, field_key):
        """Get the value of a field by its key"""
        try:
            field_def = FieldDefinition.objects.get(field_key=field_key, is_active=True)
            field_value = self.field_values.get(field=field_def)
            return field_value.value
        except (FieldDefinition.DoesNotExist, FieldValue.DoesNotExist):
            return None

    def get_expiry_date(self):
        """Get the expiry date of the guarantee"""
        return self.get_field_value('expiry_date')

    def set_field_value(self, field_key, value):
        """Set the value of a field by its key"""
        try:
            field_def = FieldDefinition.objects.get(field_key=field_key, is_active=True)
            field_value, created = FieldValue.objects.update_or_create(
                guarantee=self,
                field=field_def,
                defaults={'value': value}
            )
            return field_value
        except FieldDefinition.DoesNotExist:
            return None

class FieldValue(models.Model):
    """Model for field values in guarantees"""
    guarantee = models.ForeignKey(DynamicGuarantee, on_delete=models.CASCADE, related_name='field_values', verbose_name="الكفالة")
    field = models.ForeignKey(FieldDefinition, on_delete=models.CASCADE, related_name='values', verbose_name="الحقل")
    value = models.TextField(verbose_name="القيمة")

    class Meta:
        verbose_name = "قيمة الحقل"
        verbose_name_plural = "قيم الحقول"
        unique_together = ('guarantee', 'field')

    def __str__(self):
        return f"{self.field.name}: {self.value}"

class DynamicGuaranteeDocument(models.Model):
    """Model for guarantee documents"""
    DOCUMENT_TYPES = (
        ('guarantee_letter', 'كتاب الكفالة'),
        ('expiry_letter', 'كتاب انتهاء الكفالة'),
        ('other', 'أخرى'),
    )

    guarantee = models.ForeignKey(DynamicGuarantee, on_delete=models.CASCADE, related_name='documents', verbose_name="الكفالة")
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES, verbose_name="نوع المستند")
    document_file = models.FileField(upload_to='guarantee_documents/', verbose_name="الملف")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='uploaded_dynamic_guarantee_documents', verbose_name="تم الرفع بواسطة")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الرفع")

    class Meta:
        verbose_name = "مستند الكفالة"
        verbose_name_plural = "مستندات الكفالة"
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.get_document_type_display()} - {self.guarantee}"
