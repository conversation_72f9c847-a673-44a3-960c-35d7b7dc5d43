# نظام إدارة الدائرة القانونية في هيأة المنافذ الحدودية

## نبذة عن المشروع

نظام إدارة الدائرة القانونية هو تطبيق ويب مصمم خصيصًا لهيأة المنافذ الحدودية لإدارة الأقسام القانونية المختلفة، بما في ذلك قسم الاستشارات والكفالات وقسم الدعاوى والمجالس.

## الميزات الرئيسية

- إدارة الكفالات والضمانات
- إدارة المجالس واللجان التحقيقية
- إدارة الدعاوى القانونية
- نظام إدارة المستندات
- لوحة تحكم للإحصائيات والتقارير
- نظام إدارة المستخدمين والصلاحيات
- نظام النسخ الاحتياطي والاستعادة
- واجهة عربية بالكامل

## المتطلبات التقنية

- Python 3.8+
- Django 5.2+
- MariaDB
- HTML5, CSS3, JavaScript
- Bootstrap 5

## التثبيت والإعداد

### 1. إعداد بيئة التطوير

```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# في Windows
venv\\Scripts\\activate
# في Linux/Mac
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. إعداد قاعدة البيانات

#### استخدام SQLite (الأسهل للتطوير)

يمكنك استخدام قاعدة بيانات SQLite المضمنة مع Django للتطوير السريع:

```bash
# تنفيذ الهجرات
python manage.py migrate

# إنشاء البيانات الأولية
python manage.py create_initial_data
```

#### استخدام MariaDB (للإنتاج)

```bash
# إنشاء قاعدة بيانات MariaDB
mysql -u root -p
CREATE DATABASE council_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'council_user'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON council_system.* TO 'council_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# تعديل ملف settings.py لإعداد قاعدة البيانات
# قم بتعديل الإعدادات التالية في ملف settings.py

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'council_system',
        'USER': 'council_user',
        'PASSWORD': 'password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'use_unicode': True,
        },
    }
}

# تنفيذ الهجرات
python manage.py migrate

# إنشاء البيانات الأولية
python manage.py create_initial_data
```

### 3. إنشاء مستخدم مدير

إذا قمت بتنفيذ الأمر `create_initial_data`، فسيتم إنشاء مستخدم مدير تلقائياً بالبيانات التالية:

- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

وإلا يمكنك إنشاء مستخدم مدير يدوياً:

```bash
python manage.py createsuperuser
```

### 4. تشغيل الخادم

```bash
python manage.py runserver
```

يمكنك الآن الوصول إلى النظام من خلال الرابط التالي: http://127.0.0.1:8000/

### 5. استخدام النظام

1. قم بتسجيل الدخول باستخدام بيانات المستخدم المدير:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

2. استكشف الأقسام المختلفة للنظام:
   - **لوحة التحكم**: عرض الإحصائيات والتقارير
   - **قسم الاستشارات والكفالات**: إدارة الكفالات والضمانات
   - **قسم الدعاوى والمجالس**: إدارة المجالس واللجان التحقيقية

3. إدارة المستخدمين والأقسام من خلال لوحة الإدارة: http://127.0.0.1:8000/admin/

## هيكل المشروع

- `core`: تطبيق أساسي يحتوي على نماذج المستخدمين والإعدادات
- `consultations`: تطبيق لإدارة الاستشارات والكفالات
- `lawsuits`: تطبيق لإدارة الدعاوى والمجالس
- `dashboard`: تطبيق للوحة التحكم والتقارير
- `legal_department`: تطبيق لإدارة الدائرة القانونية

## المساهمة

للمساهمة في هذا المشروع، يرجى اتباع الخطوات التالية:

1. عمل Fork للمشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. عمل Commit للتغييرات (`git commit -m 'إضافة ميزة رائعة'`)
4. رفع الفرع (`git push origin feature/amazing-feature`)
5. فتح طلب Pull Request

## الترخيص

هذا المشروع مرخص تحت [ترخيص MIT](LICENSE).
