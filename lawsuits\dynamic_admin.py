from django.contrib import admin
from .dynamic_models import FieldDefinition, DynamicCouncil, FieldValue, DynamicCouncilMember, DynamicCouncilDocument

class FieldValueInline(admin.TabularInline):
    model = FieldValue
    extra = 1

class DynamicCouncilMemberInline(admin.TabularInline):
    model = DynamicCouncilMember
    extra = 1

class DynamicCouncilDocumentInline(admin.TabularInline):
    model = DynamicCouncilDocument
    extra = 1

class FieldDefinitionAdmin(admin.ModelAdmin):
    list_display = ('name', 'field_key', 'field_type', 'section', 'is_required', 'is_system', 'is_active', 'display_order')
    list_filter = ('field_type', 'section', 'is_required', 'is_system', 'is_active')
    search_fields = ('name', 'field_key', 'description')
    readonly_fields = ('created_by', 'created_at', 'updated_at')
    fieldsets = (
        ('معلومات الحقل', {
            'fields': ('name', 'field_key', 'field_type', 'section', 'description')
        }),
        ('خيارات الحقل', {
            'fields': ('options', 'is_required', 'is_system', 'is_active', 'display_order')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

class DynamicCouncilAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'is_closed', 'extended', 'created_by', 'created_at')
    list_filter = ('is_closed', 'extended', 'created_at')
    search_fields = ('field_values__value',)
    readonly_fields = ('created_by', 'updated_by', 'created_at', 'updated_at')
    inlines = [FieldValueInline, DynamicCouncilMemberInline, DynamicCouncilDocumentInline]
    fieldsets = (
        ('حالة المجلس', {
            'fields': ('is_closed', 'extended', 'extension_days')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

class FieldValueAdmin(admin.ModelAdmin):
    list_display = ('council', 'field', 'value')
    list_filter = ('field__section', 'field__field_type')
    search_fields = ('value', 'field__name', 'council__field_values__value')

class DynamicCouncilMemberAdmin(admin.ModelAdmin):
    list_display = ('name', 'position', 'council', 'is_head', 'created_by', 'created_at')
    list_filter = ('is_head', 'created_at')
    search_fields = ('name', 'position', 'council__field_values__value')
    readonly_fields = ('created_by', 'created_at')

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

class DynamicCouncilDocumentAdmin(admin.ModelAdmin):
    list_display = ('council', 'document_type', 'description', 'uploaded_by', 'uploaded_at')
    list_filter = ('document_type', 'uploaded_at')
    search_fields = ('description', 'council__field_values__value')
    readonly_fields = ('uploaded_by', 'uploaded_at')

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)
