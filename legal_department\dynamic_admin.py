from django.contrib import admin
from .dynamic_models import FieldDefinition, DynamicLegalCase, FieldValue, DynamicLegalDocument, DynamicCaseNote

class FieldValueInline(admin.TabularInline):
    model = FieldValue
    extra = 1

class DynamicLegalDocumentInline(admin.TabularInline):
    model = DynamicLegalDocument
    extra = 1

class DynamicCaseNoteInline(admin.TabularInline):
    model = DynamicCaseNote
    extra = 1

class FieldDefinitionAdmin(admin.ModelAdmin):
    list_display = ('name', 'field_key', 'field_type', 'section', 'is_required', 'is_system', 'is_active', 'display_order')
    list_filter = ('field_type', 'section', 'is_required', 'is_system', 'is_active')
    search_fields = ('name', 'field_key', 'description')
    readonly_fields = ('created_by', 'created_at', 'updated_at')
    fieldsets = (
        ('معلومات الحقل', {
            'fields': ('name', 'field_key', 'field_type', 'section', 'description')
        }),
        ('خيارات الحقل', {
            'fields': ('options', 'is_required', 'is_system', 'is_active', 'display_order')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

class DynamicLegalCaseAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'status', 'department', 'assigned_to', 'created_by', 'created_at')
    list_filter = ('status', 'department', 'created_at')
    search_fields = ('field_values__value',)
    readonly_fields = ('created_by', 'updated_by', 'created_at', 'updated_at')
    inlines = [FieldValueInline, DynamicLegalDocumentInline, DynamicCaseNoteInline]
    fieldsets = (
        ('حالة الدعوى', {
            'fields': ('status', 'department', 'assigned_to')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'updated_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

class FieldValueAdmin(admin.ModelAdmin):
    list_display = ('case', 'field', 'value')
    list_filter = ('field__section', 'field__field_type')
    search_fields = ('value', 'field__name', 'case__field_values__value')

class DynamicLegalDocumentAdmin(admin.ModelAdmin):
    list_display = ('title', 'case', 'uploaded_by', 'uploaded_at')
    list_filter = ('uploaded_at',)
    search_fields = ('title', 'description', 'case__field_values__value')
    readonly_fields = ('uploaded_by', 'uploaded_at')

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)

class DynamicCaseNoteAdmin(admin.ModelAdmin):
    list_display = ('case', 'note', 'created_by', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('note', 'case__field_values__value')
    readonly_fields = ('created_by', 'created_at')

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
