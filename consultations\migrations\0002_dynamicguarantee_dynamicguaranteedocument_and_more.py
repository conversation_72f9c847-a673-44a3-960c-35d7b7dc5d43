# Generated by Django 5.2 on 2025-04-15 19:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('consultations', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DynamicGuarantee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='فعالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_dynamic_guarantees', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_dynamic_guarantees', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'كفالة ديناميكية',
                'verbose_name_plural': 'الكفالات الديناميكية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DynamicGuaranteeDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('guarantee_letter', 'كتاب الكفالة'), ('expiry_letter', 'كتاب انتهاء الكفالة'), ('other', 'أخرى')], max_length=20, verbose_name='نوع المستند')),
                ('document_file', models.FileField(upload_to='guarantee_documents/', verbose_name='الملف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('guarantee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='consultations.dynamicguarantee', verbose_name='الكفالة')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_dynamic_guarantee_documents', to=settings.AUTH_USER_MODEL, verbose_name='تم الرفع بواسطة')),
            ],
            options={
                'verbose_name': 'مستند الكفالة',
                'verbose_name_plural': 'مستندات الكفالة',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='FieldDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الحقل')),
                ('field_key', models.CharField(max_length=100, unique=True, verbose_name='مفتاح الحقل')),
                ('field_type', models.CharField(choices=[('text', 'نص'), ('number', 'رقم'), ('date', 'تاريخ'), ('boolean', 'نعم/لا')], max_length=20, verbose_name='نوع الحقل')),
                ('is_required', models.BooleanField(default=False, verbose_name='مطلوب')),
                ('is_system', models.BooleanField(default=False, verbose_name='حقل نظام')),
                ('is_active', models.BooleanField(default=True, verbose_name='فعال')),
                ('display_order', models.IntegerField(default=0, verbose_name='ترتيب العرض')),
                ('section', models.CharField(choices=[('basic', 'المعلومات الأساسية'), ('additional', 'معلومات إضافية')], default='basic', max_length=50, verbose_name='القسم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_field_definitions', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_field_definitions', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'تعريف الحقل',
                'verbose_name_plural': 'تعريفات الحقول',
                'ordering': ['display_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='FieldValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.TextField(verbose_name='القيمة')),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='values', to='consultations.fielddefinition', verbose_name='الحقل')),
                ('guarantee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='field_values', to='consultations.dynamicguarantee', verbose_name='الكفالة')),
            ],
            options={
                'verbose_name': 'قيمة الحقل',
                'verbose_name_plural': 'قيم الحقول',
                'unique_together': {('guarantee', 'field')},
            },
        ),
    ]
