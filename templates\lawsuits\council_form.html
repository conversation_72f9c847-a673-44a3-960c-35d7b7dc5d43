{% extends 'base/base.html' %}

{% block title %}
{% if is_edit %}تعديل مجلس/لجنة{% else %}إضافة مجلس/لجنة جديدة{% endif %} - هيأة المنافذ الحدودية
{% endblock %}

{% block page_title %}
{% if is_edit %}تعديل مجلس/لجنة{% else %}إضافة مجلس/لجنة جديدة{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <a href="{% if is_edit %}{% url 'lawsuits:council_detail' council.id %}{% else %}{% url 'lawsuits:council_list' %}{% endif %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i> العودة
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% if is_edit %}تعديل مجلس/لجنة{% else %}إضافة مجلس/لجنة جديدة{% endif %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">المعلومات الأساسية</h5>
                                
                                <div class="mb-3">
                                    <label for="council_type" class="form-label">نوع المجلس/اللجنة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="council_type" name="council_type" required>
                                        <option value="" {% if not council %}selected{% endif %} disabled>اختر نوع المجلس/اللجنة</option>
                                        {% for type_code, type_name in council_types %}
                                        <option value="{{ type_code }}" {% if council and council.council_type == type_code %}selected{% endif %}>{{ type_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="formation_order" class="form-label">أمر التشكيل <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="formation_order" name="formation_order" value="{% if council %}{{ council.formation_order }}{% endif %}" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="formation_date" class="form-label">تاريخ أمر التشكيل <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="formation_date" name="formation_date" value="{% if council %}{{ council.formation_date|date:'Y-m-d' }}{% endif %}" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="port_name" class="form-label">اسم المنفذ أو الدائرة <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="port_name" name="port_name" value="{% if council %}{{ council.port_name }}{% endif %}" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="mb-3">معلومات إضافية</h5>
                                
                                <div class="mb-3">
                                    <label for="duration" class="form-label">المدة المحددة (بالأيام) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="duration" name="duration" value="{% if council %}{{ council.duration }}{% endif %}" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subject" class="form-label">موضوع المجلس/اللجنة <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="subject" name="subject" rows="3" required>{% if council %}{{ council.subject }}{% endif %}</textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="2">{% if council %}{{ council.notes }}{% endif %}</textarea>
                                </div>
                                
                                {% if is_edit %}
                                <div class="mb-3">
                                    <label for="last_action" class="form-label">آخر إجراء</label>
                                    <textarea class="form-control" id="last_action" name="last_action" rows="2">{% if council %}{{ council.last_action }}{% endif %}</textarea>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if not is_edit %}
                        <hr>
                        <h5 class="mb-3">أعضاء المجلس/اللجنة</h5>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            يمكنك إضافة أعضاء المجلس/اللجنة الآن أو لاحقاً من صفحة تفاصيل المجلس/اللجنة.
                        </div>
                        
                        <div id="council-members">
                            <div class="row mb-3 council-member">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" name="member_name_0" placeholder="اسم العضو">
                                </div>
                                <div class="col-md-4">
                                    <input type="text" class="form-control" name="member_position_0" placeholder="مقر العمل">
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input member-head-checkbox" type="checkbox" name="member_is_head_0" id="member_is_head_0">
                                        <label class="form-check-label" for="member_is_head_0">
                                            رئيس اللجنة
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <button type="button" class="btn btn-danger remove-member-button" disabled>
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <input type="hidden" id="member_count" name="member_count" value="1">
                        
                        <div class="mb-4">
                            <button type="button" id="add-member-button" class="btn btn-success">
                                <i class="fas fa-plus-circle me-2"></i> إضافة عضو آخر
                            </button>
                        </div>
                        
                        <hr>
                        <h5 class="mb-3">رفع كتاب التشكيل</h5>
                        <div class="mb-3">
                            <label for="formation_letter" class="form-label">كتاب التشكيل</label>
                            <input type="file" class="form-control" id="formation_letter" name="formation_letter">
                            <div class="form-text">يمكنك رفع كتاب التشكيل الآن أو لاحقاً من صفحة تفاصيل المجلس/اللجنة.</div>
                        </div>
                        {% endif %}
                        
                        <div class="text-center mt-4">
                            <a href="{% if is_edit %}{% url 'lawsuits:council_detail' council.id %}{% else %}{% url 'lawsuits:council_list' %}{% endif %}" class="btn btn-secondary me-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">
                                {% if is_edit %}حفظ التغييرات{% else %}إضافة المجلس/اللجنة{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Council members management
        const addMemberButton = document.getElementById('add-member-button');
        if (addMemberButton) {
            addMemberButton.addEventListener('click', function() {
                const membersContainer = document.getElementById('council-members');
                const memberCount = parseInt(document.getElementById('member_count').value);
                
                const memberTemplate = `
                    <div class="row mb-3 council-member">
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="member_name_${memberCount}" placeholder="اسم العضو">
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" name="member_position_${memberCount}" placeholder="مقر العمل">
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input member-head-checkbox" type="checkbox" name="member_is_head_${memberCount}" id="member_is_head_${memberCount}">
                                <label class="form-check-label" for="member_is_head_${memberCount}">
                                    رئيس اللجنة
                                </label>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <button type="button" class="btn btn-danger remove-member-button">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
                
                membersContainer.insertAdjacentHTML('beforeend', memberTemplate);
                
                // Enable the first remove button if we have more than one member
                if (memberCount === 1) {
                    document.querySelector('.remove-member-button').removeAttribute('disabled');
                }
                
                // Update the member count
                document.getElementById('member_count').value = memberCount + 1;
            });
            
            // Remove council member
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('remove-member-button') || e.target.closest('.remove-member-button')) {
                    const memberRow = e.target.closest('.council-member');
                    memberRow.remove();
                    
                    // Update the member count
                    const membersContainer = document.getElementById('council-members');
                    const memberCount = membersContainer.children.length;
                    document.getElementById('member_count').value = memberCount;
                    
                    // Disable the last remove button if only one member remains
                    if (memberCount === 1) {
                        document.querySelector('.remove-member-button').setAttribute('disabled', 'disabled');
                    }
                }
            });
            
            // Ensure only one member is head
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('member-head-checkbox') && e.target.checked) {
                    const checkboxes = document.querySelectorAll('.member-head-checkbox');
                    checkboxes.forEach(checkbox => {
                        if (checkbox !== e.target) {
                            checkbox.checked = false;
                        }
                    });
                }
            });
        }
    });
</script>
{% endblock %}
{% endblock %}
