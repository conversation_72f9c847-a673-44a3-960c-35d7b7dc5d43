from django.contrib import admin
from .models import LegalCase, LegalDocument, CaseNote

# Import dynamic models
from .dynamic_models import FieldDefinition, DynamicLegalCase, FieldValue, DynamicLegalDocument, DynamicCaseNote
from .dynamic_admin import FieldDefinitionAdmin, DynamicLegalCaseAdmin, FieldValueAdmin, DynamicLegalDocumentAdmin, DynamicCaseNoteAdmin

# Register dynamic models
admin.site.register(FieldDefinition, FieldDefinitionAdmin)
admin.site.register(DynamicLegalCase, DynamicLegalCaseAdmin)
admin.site.register(FieldValue, FieldValueAdmin)
admin.site.register(DynamicLegalDocument, DynamicLegalDocumentAdmin)
admin.site.register(DynamicCaseNote, DynamicCaseNoteAdmin)

class LegalDocumentInline(admin.TabularInline):
    model = LegalDocument
    extra = 1

class CaseNoteInline(admin.TabularInline):
    model = CaseNote
    extra = 1

@admin.register(LegalCase)
class LegalCaseAdmin(admin.ModelAdmin):
    list_display = ('title', 'case_number', 'case_type', 'status', 'department', 'assigned_to', 'start_date', 'end_date')
    list_filter = ('status', 'case_type', 'department', 'start_date')
    search_fields = ('title', 'case_number', 'description')
    date_hierarchy = 'start_date'
    readonly_fields = ('created_by', 'updated_by', 'created_at', 'updated_at')
    inlines = [LegalDocumentInline, CaseNoteInline]

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(LegalDocument)
class LegalDocumentAdmin(admin.ModelAdmin):
    list_display = ('title', 'case', 'uploaded_by', 'uploaded_at')
    list_filter = ('uploaded_at',)
    search_fields = ('title', 'case__title', 'case__case_number', 'description')
    readonly_fields = ('uploaded_by', 'uploaded_at')

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.uploaded_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(CaseNote)
class CaseNoteAdmin(admin.ModelAdmin):
    list_display = ('case', 'created_by', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('note', 'case__title', 'case__case_number')
    readonly_fields = ('created_by', 'created_at')

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
