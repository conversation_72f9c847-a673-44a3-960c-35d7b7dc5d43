{% extends 'base/base.html' %}
{% load legal_department_extras %}

{% block title %}الدعاوى القانونية الديناميكية{% endblock %}

{% block page_title %}الدعاوى القانونية الديناميكية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">الدعاوى القانونية الديناميكية</h5>
                    <div>
                        <a href="{% url 'legal_department:dynamic_case_create' %}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus-circle"></i> إضافة دعوى جديدة
                        </a>
                        <a href="{% url 'legal_department:field_definition_list' %}" class="btn btn-warning btn-sm ms-2">
                            <i class="fas fa-cogs"></i> إدارة تعريفات الحقول
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <form method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" placeholder="بحث..." value="{{ search_query }}">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="status">
                                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>جميع الحالات</option>
                                    <option value="open" {% if status_filter == 'open' %}selected{% endif %}>مفتوحة</option>
                                    <option value="closed" {% if status_filter == 'closed' %}selected{% endif %}>مغلقة</option>
                                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" name="case_type">
                                    <option value="">نوع الدعوى</option>
                                    {% for case_type in case_types %}
                                    <option value="{{ case_type }}" {% if case_type_filter == case_type %}selected{% endif %}>{{ case_type }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control" name="start_date" placeholder="من تاريخ" value="{{ start_date }}">
                            </div>
                            <div class="col-md-2">
                                <input type="date" class="form-control" name="end_date" placeholder="إلى تاريخ" value="{{ end_date }}">
                            </div>
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter"></i> تصفية
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Cases Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>عنوان الدعوى</th>
                                    <th>رقم الدعوى</th>
                                    <th>نوع الدعوى</th>
                                    <th>القسم</th>
                                    <th>تاريخ البدء</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for case in page_obj %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ case|get_case_field:'title' }}</td>
                                    <td>{{ case|get_case_field:'case_number' }}</td>
                                    <td>{{ case|get_case_field:'case_type' }}</td>
                                    <td>{{ case.department.name }}</td>
                                    <td>{{ case|get_case_field:'start_date' }}</td>
                                    <td>
                                        {% if case.status == 'open' %}
                                            <span class="badge bg-success">مفتوحة</span>
                                        {% elif case.status == 'closed' %}
                                            <span class="badge bg-secondary">مغلقة</span>
                                        {% else %}
                                            <span class="badge bg-warning">قيد الانتظار</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'legal_department:dynamic_case_detail' case.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'legal_department:dynamic_case_edit' case.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'legal_department:dynamic_case_delete' case.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد دعاوى</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if case_type_filter %}&case_type={{ case_type_filter }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if case_type_filter %}&case_type={{ case_type_filter }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if case_type_filter %}&case_type={{ case_type_filter }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}">{{ num }}</a>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if case_type_filter %}&case_type={{ case_type_filter }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if case_type_filter %}&case_type={{ case_type_filter }}{% endif %}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
