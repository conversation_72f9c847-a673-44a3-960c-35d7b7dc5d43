{% extends 'base/base.html' %}
{% load consultations_extras %}

{% block title %}قائمة الكفالات - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}قائمة الكفالات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{% url 'consultations:index' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i> العودة إلى الصفحة الرئيسية
                    </a>
                </div>
                <div>
                    <a href="{% url 'consultations:dynamic_guarantee_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> إضافة كفالة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">بحث وتصفية</h5>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">بحث</label>
                            <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="ابحث عن اسم الكفيل، اسم المكفول، اسم المصرف...">
                        </div>
                        <div class="col-md-4">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>الكل</option>
                                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>فعالة</option>
                                <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير فعالة</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i> بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">الكفالات</h5>
                    <div>
                        <a href="{% url 'consultations:export_guarantees_to_excel' %}" class="btn btn-sm btn-success">
                            <i class="fas fa-file-excel me-1"></i> تصدير Excel
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الكفيل</th>
                                    <th>اسم المكفول</th>
                                    <th>مبلغ الكفالة</th>
                                    <th>المصرف المانح</th>
                                    <th>تاريخ الإصدار</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for guarantee in page_obj %}
                                <tr>
                                    <td>{{ guarantee|get_guarantee_field:'guarantor_name' }}</td>
                                    <td>{{ guarantee|get_guarantee_field:'guaranteed_name' }}</td>
                                    <td>{{ guarantee|get_guarantee_field:'guarantee_amount' }}</td>
                                    <td>{{ guarantee|get_guarantee_field:'bank_name' }}</td>
                                    <td>{{ guarantee|get_guarantee_field:'issue_date' }}</td>
                                    <td>{{ guarantee|get_guarantee_field:'expiry_date' }}</td>
                                    <td>
                                        {% if guarantee.is_active %}
                                            <span class="badge bg-success">فعالة</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير فعالة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'consultations:dynamic_guarantee_detail' guarantee.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'consultations:dynamic_guarantee_edit' guarantee.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'consultations:dynamic_guarantee_delete' guarantee.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد كفالات</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item"><a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a></li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
