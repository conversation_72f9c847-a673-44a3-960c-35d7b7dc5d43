from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from core.models import Department, UserProfile, SystemSetting
from dashboard.models import Notification

class Command(BaseCommand):
    help = 'Creates initial data for the system'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating initial data...'))
        
        # Create superuser if it doesn't exist
        if not User.objects.filter(username='admin').exists():
            admin_user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='مدير',
                last_name='النظام'
            )
            self.stdout.write(self.style.SUCCESS('Superuser created successfully'))
            
            # Create admin profile
            UserProfile.objects.create(
                user=admin_user,
                user_type='admin',
                phone='07700000000'
            )
            self.stdout.write(self.style.SUCCESS('Admin profile created successfully'))
        else:
            admin_user = User.objects.get(username='admin')
            self.stdout.write(self.style.WARNING('Superuser already exists'))
        
        # Create departments if they don't exist
        departments = [
            {'name': 'قسم الاستشارات والكفالات', 'description': 'قسم مختص بالاستشارات والكفالات القانونية'},
            {'name': 'قسم الدعاوى والمجالس', 'description': 'قسم مختص بالدعاوى والمجالس التحقيقية'},
        ]
        
        for dept_data in departments:
            dept, created = Department.objects.get_or_create(
                name=dept_data['name'],
                defaults={'description': dept_data['description']}
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Department "{dept.name}" created successfully'))
            else:
                self.stdout.write(self.style.WARNING(f'Department "{dept.name}" already exists'))
        
        # Create system settings if they don't exist
        settings = [
            {'key': 'system_name', 'value': 'نظام إدارة الدائرة القانونية', 'description': 'اسم النظام'},
            {'key': 'organization_name', 'value': 'هيأة المنافذ الحدودية', 'description': 'اسم المؤسسة'},
            {'key': 'department_name', 'value': 'الدائرة القانونية', 'description': 'اسم الدائرة'},
            {'key': 'system_version', 'value': '1.0.0', 'description': 'إصدار النظام'},
        ]
        
        for setting_data in settings:
            setting, created = SystemSetting.objects.get_or_create(
                key=setting_data['key'],
                defaults={
                    'value': setting_data['value'],
                    'description': setting_data['description']
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Setting "{setting.key}" created successfully'))
            else:
                self.stdout.write(self.style.WARNING(f'Setting "{setting.key}" already exists'))
        
        # Create welcome notification for admin
        Notification.objects.get_or_create(
            user=admin_user,
            title='مرحباً بك في نظام إدارة الدائرة القانونية',
            defaults={
                'message': 'مرحباً بك في نظام إدارة الدائرة القانونية لهيأة المنافذ الحدودية. يمكنك الآن البدء باستخدام النظام.',
                'notification_type': 'info',
                'is_read': False,
                'created_at': timezone.now()
            }
        )
        
        self.stdout.write(self.style.SUCCESS('Initial data created successfully'))
