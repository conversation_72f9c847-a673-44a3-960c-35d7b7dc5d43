# Generated by Django 5.2 on 2025-04-09 20:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف القسم')),
                ('is_active', models.BooleanField(default=True, verbose_name='فعال')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SystemSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=50, unique=True, verbose_name='المفتاح')),
                ('value', models.TextField(verbose_name='القيمة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
            ],
            options={
                'verbose_name': 'إعداد النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('backup', 'نسخ احتياطي'), ('restore', 'استعادة')], max_length=20, verbose_name='الإجراء')),
                ('model_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم النموذج')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الكائن')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='التاريخ والوقت')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل التدقيق',
                'verbose_name_plural': 'سجلات التدقيق',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_type', models.CharField(choices=[('admin', 'مدير النظام'), ('department_head', 'رئيس قسم'), ('data_entry', 'مدخل بيانات'), ('general_manager', 'المدير العام')], default='data_entry', max_length=20, verbose_name='نوع المستخدم')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='profile_pictures/', verbose_name='الصورة الشخصية')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='core.department', verbose_name='القسم')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف المستخدم',
                'verbose_name_plural': 'ملفات المستخدمين',
            },
        ),
    ]
