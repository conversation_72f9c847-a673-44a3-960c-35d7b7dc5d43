{% extends 'base/base.html' %}

{% block title %}
{% if is_edit %}تعديل حقل{% else %}إضافة حقل جديد{% endif %} - هيأة المنافذ الحدودية
{% endblock %}

{% block page_title %}
{% if is_edit %}تعديل حقل{% else %}إضافة حقل جديد{% endif %}
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-3">
        <div class="col-md-12">
            <a href="{% url 'consultations:field_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة الحقول
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% if is_edit %}تعديل حقل{% else %}إضافة حقل جديد{% endif %}</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        يمكنك إضافة حقول جديدة لنموذج الكفالة حسب احتياجاتك. ستظهر الحقول الجديدة مع الحقول الأساسية في نموذج الكفالة.
                    </div>

                    <form method="post">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="name" class="form-label">اسم الحقل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{% if field %}{{ field.name }}{% endif %}" required>
                        </div>

                        <div class="mb-3">
                            <label for="field_type" class="form-label">نوع الحقل <span class="text-danger">*</span></label>
                            <select class="form-select" id="field_type" name="field_type" required {% if is_edit %}disabled{% endif %}>
                                <option value="" {% if not field %}selected{% endif %} disabled>اختر نوع الحقل</option>
                                <option value="text" {% if field and field.field_type == 'text' %}selected{% endif %}>نص</option>
                                <option value="number" {% if field and field.field_type == 'number' %}selected{% endif %}>رقم</option>
                                <option value="date" {% if field and field.field_type == 'date' %}selected{% endif %}>تاريخ</option>
                                <option value="boolean" {% if field and field.field_type == 'boolean' %}selected{% endif %}>نعم/لا</option>
                            </select>
                            {% if is_edit %}
                            <input type="hidden" name="field_type" value="{{ field.field_type }}">
                            <div class="form-text">لا يمكن تغيير نوع الحقل بعد إنشائه.</div>
                            {% endif %}
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_required" name="is_required" {% if field and field.is_required %}checked{% endif %}>
                            <label class="form-check-label" for="is_required">حقل مطلوب</label>
                        </div>

                        {% if is_edit %}
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {% if field and field.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">فعال</label>
                            <div class="form-text">إلغاء تفعيل الحقل سيخفيه من نموذج الكفالة.</div>
                        </div>
                        {% endif %}

                        <div class="text-center mt-4">
                            <a href="{% url 'consultations:field_list' %}" class="btn btn-secondary me-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">
                                {% if is_edit %}حفظ التغييرات{% else %}إضافة الحقل{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
