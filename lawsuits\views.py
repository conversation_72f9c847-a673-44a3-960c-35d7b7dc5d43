from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext as _
from django.db.models import Q
from django.core.paginator import Paginator
from django.utils import timezone

from .models import Council, CouncilMember, CouncilDocument
from core.models import AuditLog

@login_required
def index(request):
    """Lawsuits home page"""
    # Get counts
    council_count = Council.objects.count()
    active_council_count = Council.objects.filter(is_closed=False).count()
    expired_council_count = sum(1 for council in Council.objects.filter(is_closed=False) if council.is_expired())

    # Get recent councils
    recent_councils = Council.objects.all().order_by('-formation_date')[:5]

    context = {
        'council_count': council_count,
        'active_council_count': active_council_count,
        'expired_council_count': expired_council_count,
        'recent_councils': recent_councils,
        'today': timezone.now().date(),
    }

    return render(request, 'lawsuits/index.html', context)

@login_required
def council_list(request):
    """List all councils"""
    # Get filter parameters
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', 'all')
    council_type_filter = request.GET.get('council_type', '')
    start_date = request.GET.get('start_date', '')
    end_date = request.GET.get('end_date', '')

    # Base queryset
    councils = Council.objects.all()

    # Apply filters
    if search_query:
        councils = councils.filter(
            Q(formation_order__icontains=search_query) |
            Q(port_name__icontains=search_query) |
            Q(subject__icontains=search_query) |
            Q(last_action__icontains=search_query)
        )

    if status_filter == 'active':
        councils = councils.filter(is_closed=False)
    elif status_filter == 'closed':
        councils = councils.filter(is_closed=True)
    elif status_filter == 'expired':
        today = timezone.now().date()
        # We need to filter in Python because is_expired is a method, not a field
        councils = [council for council in councils if council.is_expired()]

    if council_type_filter:
        councils = councils.filter(council_type=council_type_filter)

    if start_date:
        councils = councils.filter(formation_date__gte=start_date)

    if end_date:
        councils = councils.filter(formation_date__lte=end_date)

    # Order by
    order_by = request.GET.get('order_by', '-formation_date')
    if isinstance(councils, list):  # If we filtered by is_expired
        if order_by.startswith('-'):
            reverse = True
            order_attr = order_by[1:]
        else:
            reverse = False
            order_attr = order_by
        councils = sorted(councils, key=lambda x: getattr(x, order_attr), reverse=reverse)
    else:
        councils = councils.order_by(order_by)

    # Pagination
    paginator = Paginator(councils, 10)  # Show 10 councils per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'council_type_filter': council_type_filter,
        'start_date': start_date,
        'end_date': end_date,
        'order_by': order_by,
        'today': timezone.now().date(),
        'council_types': Council.COUNCIL_TYPES,
    }

    return render(request, 'lawsuits/council_list.html', context)

@login_required
def council_create(request):
    """Create a new council"""
    if request.method == 'POST':
        # Get form data
        council_type = request.POST.get('council_type')
        formation_order = request.POST.get('formation_order')
        formation_date = request.POST.get('formation_date')
        port_name = request.POST.get('port_name')
        duration = request.POST.get('duration')
        subject = request.POST.get('subject')
        notes = request.POST.get('notes')

        # Create council
        council = Council.objects.create(
            council_type=council_type,
            formation_order=formation_order,
            formation_date=formation_date,
            port_name=port_name,
            duration=int(duration),
            subject=subject,
            notes=notes,
            is_closed=False,
            created_by=request.user,
            updated_by=request.user,
        )

        # Handle council members
        member_count = int(request.POST.get('member_count', 0))
        for i in range(member_count):
            name = request.POST.get(f'member_name_{i}')
            position = request.POST.get(f'member_position_{i}')
            is_head = request.POST.get(f'member_is_head_{i}') == 'on'

            if name and position:
                CouncilMember.objects.create(
                    council=council,
                    name=name,
                    position=position,
                    is_head=is_head
                )

        # Handle formation letter document
        if 'formation_letter' in request.FILES:
            CouncilDocument.objects.create(
                council=council,
                document_type='formation_letter',
                document_file=request.FILES['formation_letter'],
                description=_('كتاب التشكيل'),
                uploaded_by=request.user
            )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='Council',
            object_id=council.id,
            description=_('تم إنشاء مجلس/لجنة جديدة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إنشاء المجلس/اللجنة بنجاح'))
        return redirect('lawsuits:council_detail', council_id=council.id)

    context = {
        'council_types': Council.COUNCIL_TYPES,
    }

    return render(request, 'lawsuits/council_form.html', context)

@login_required
def council_detail(request, council_id):
    """Display council details"""
    council = get_object_or_404(Council, id=council_id)
    members = council.members.all()
    documents = council.documents.all()

    context = {
        'council': council,
        'members': members,
        'documents': documents,
        'today': timezone.now().date(),
        'days_remaining': council.days_remaining(),
        'is_expired': council.is_expired(),
    }

    return render(request, 'lawsuits/council_detail.html', context)

@login_required
def council_edit(request, council_id):
    """Edit an existing council"""
    council = get_object_or_404(Council, id=council_id)

    if request.method == 'POST':
        # Update council
        council.council_type = request.POST.get('council_type')
        council.formation_order = request.POST.get('formation_order')
        council.formation_date = request.POST.get('formation_date')
        council.port_name = request.POST.get('port_name')
        council.duration = int(request.POST.get('duration'))
        council.subject = request.POST.get('subject')
        council.notes = request.POST.get('notes')
        council.last_action = request.POST.get('last_action')
        council.updated_by = request.user
        council.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='Council',
            object_id=council.id,
            description=_('تم تحديث المجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث المجلس/اللجنة بنجاح'))
        return redirect('lawsuits:council_detail', council_id=council.id)

    context = {
        'council': council,
        'council_types': Council.COUNCIL_TYPES,
        'is_edit': True,
    }

    return render(request, 'lawsuits/council_form.html', context)

@login_required
def council_delete(request, council_id):
    """Delete a council"""
    council = get_object_or_404(Council, id=council_id)

    if request.method == 'POST':
        # Log the action before deletion
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            model_name='Council',
            object_id=council.id,
            description=_('تم حذف المجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        council.delete()
        messages.success(request, _('تم حذف المجلس/اللجنة بنجاح'))
        return redirect('lawsuits:council_list')

    context = {
        'council': council,
    }

    return render(request, 'lawsuits/council_confirm_delete.html', context)

@login_required
def council_close(request, council_id):
    """Close a council"""
    council = get_object_or_404(Council, id=council_id)

    if council.is_closed:
        messages.warning(request, _('هذا المجلس/اللجنة مغلق بالفعل'))
        return redirect('lawsuits:council_detail', council_id=council.id)

    if request.method == 'POST':
        closure_date = request.POST.get('closure_date')
        file_name = request.POST.get('file_name')

        # Update council
        council.is_closed = True
        council.closure_date = closure_date
        council.file_name = file_name
        council.updated_by = request.user
        council.save()

        # Handle closure letter document
        if 'closure_letter' in request.FILES:
            CouncilDocument.objects.create(
                council=council,
                document_type='closure_letter',
                document_file=request.FILES['closure_letter'],
                description=_('كتاب الاغلاق'),
                uploaded_by=request.user
            )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='Council',
            object_id=council.id,
            description=_('تم إغلاق المجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إغلاق المجلس/اللجنة بنجاح'))
        return redirect('lawsuits:council_detail', council_id=council.id)

    context = {
        'council': council,
    }

    return render(request, 'lawsuits/council_close.html', context)

@login_required
def council_extend(request, council_id):
    """Extend a council"""
    council = get_object_or_404(Council, id=council_id)

    if council.is_closed:
        messages.warning(request, _('لا يمكن تمديد مجلس/لجنة مغلقة'))
        return redirect('lawsuits:council_detail', council_id=council.id)

    if request.method == 'POST':
        extension_days = int(request.POST.get('extension_days', 0))

        # Check if extension is valid (max 60 days)
        if extension_days <= 0 or extension_days > 60:
            messages.error(request, _('يجب أن تكون مدة التمديد بين 1 و 60 يوم'))
            return redirect('lawsuits:council_extend', council_id=council.id)

        # Update council
        council.extended = True
        council.extension_days = extension_days
        council.updated_by = request.user
        council.save()

        # Handle extension letter document
        if 'extension_letter' in request.FILES:
            CouncilDocument.objects.create(
                council=council,
                document_type='extension_letter',
                document_file=request.FILES['extension_letter'],
                description=_('كتاب التمديد'),
                uploaded_by=request.user
            )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='Council',
            object_id=council.id,
            description=_('تم تمديد المجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تمديد المجلس/اللجنة بنجاح'))
        return redirect('lawsuits:council_detail', council_id=council.id)

    context = {
        'council': council,
    }

    return render(request, 'lawsuits/council_extend.html', context)

@login_required
def council_members(request, council_id):
    """Manage council members"""
    council = get_object_or_404(Council, id=council_id)
    members = council.members.all()

    context = {
        'council': council,
        'members': members,
    }

    return render(request, 'lawsuits/council_members.html', context)

@login_required
def add_council_member(request, council_id):
    """Add a council member"""
    council = get_object_or_404(Council, id=council_id)

    if request.method == 'POST':
        name = request.POST.get('name')
        position = request.POST.get('position')
        is_head = request.POST.get('is_head') == 'on'

        # If this member is head, unset other heads
        if is_head:
            council.members.filter(is_head=True).update(is_head=False)

        # Create member
        member = CouncilMember.objects.create(
            council=council,
            name=name,
            position=position,
            is_head=is_head
        )

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='create',
            model_name='CouncilMember',
            object_id=member.id,
            description=_('تم إضافة عضو جديد للمجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم إضافة العضو بنجاح'))
        return redirect('lawsuits:council_members', council_id=council.id)

    context = {
        'council': council,
    }

    return render(request, 'lawsuits/council_member_form.html', context)

@login_required
def edit_council_member(request, council_id, member_id):
    """Edit a council member"""
    council = get_object_or_404(Council, id=council_id)
    member = get_object_or_404(CouncilMember, id=member_id, council=council)

    if request.method == 'POST':
        name = request.POST.get('name')
        position = request.POST.get('position')
        is_head = request.POST.get('is_head') == 'on'

        # If this member is head, unset other heads
        if is_head and not member.is_head:
            council.members.filter(is_head=True).update(is_head=False)

        # Update member
        member.name = name
        member.position = position
        member.is_head = is_head
        member.save()

        # Log the action
        AuditLog.objects.create(
            user=request.user,
            action='update',
            model_name='CouncilMember',
            object_id=member.id,
            description=_('تم تحديث عضو المجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, _('تم تحديث العضو بنجاح'))
        return redirect('lawsuits:council_members', council_id=council.id)

    context = {
        'council': council,
        'member': member,
        'is_edit': True,
    }

    return render(request, 'lawsuits/council_member_form.html', context)

@login_required
def delete_council_member(request, council_id, member_id):
    """Delete a council member"""
    council = get_object_or_404(Council, id=council_id)
    member = get_object_or_404(CouncilMember, id=member_id, council=council)

    if request.method == 'POST':
        # Log the action before deletion
        AuditLog.objects.create(
            user=request.user,
            action='delete',
            model_name='CouncilMember',
            object_id=member.id,
            description=_('تم حذف عضو المجلس/اللجنة'),
            ip_address=request.META.get('REMOTE_ADDR')
        )

        member.delete()
        messages.success(request, _('تم حذف العضو بنجاح'))
        return redirect('lawsuits:council_members', council_id=council.id)

    context = {
        'council': council,
        'member': member,
    }

    return render(request, 'lawsuits/council_member_confirm_delete.html', context)

@login_required
def upload_council_document(request, council_id):
    """Upload a document for a council"""
    council = get_object_or_404(Council, id=council_id)

    if request.method == 'POST':
        document_type = request.POST.get('document_type')
        description = request.POST.get('description')
        document_file = request.FILES.get('document_file')

        if document_file:
            document = CouncilDocument.objects.create(
                council=council,
                document_type=document_type,
                document_file=document_file,
                description=description,
                uploaded_by=request.user
            )

            # Log the action
            AuditLog.objects.create(
                user=request.user,
                action='create',
                model_name='CouncilDocument',
                object_id=document.id,
                description=_('تم رفع مستند جديد للمجلس/اللجنة'),
                ip_address=request.META.get('REMOTE_ADDR')
            )

            messages.success(request, _('تم رفع المستند بنجاح'))
        else:
            messages.error(request, _('يرجى اختيار ملف لرفعه'))

        return redirect('lawsuits:council_detail', council_id=council.id)

    context = {
        'council': council,
    }

    return render(request, 'lawsuits/upload_document.html', context)
