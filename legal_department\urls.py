from django.urls import path
from . import views
from . import dynamic_views

app_name = 'legal_department'

urlpatterns = [
    path('', views.index, name='index'),
    path('cases/', views.case_list, name='case_list'),
    path('cases/create/', views.case_create, name='case_create'),
    path('cases/<int:case_id>/', views.case_detail, name='case_detail'),
    path('cases/<int:case_id>/edit/', views.case_edit, name='case_edit'),
    path('cases/<int:case_id>/delete/', views.case_delete, name='case_delete'),
    path('cases/<int:case_id>/notes/', views.case_notes, name='case_notes'),
    path('cases/<int:case_id>/notes/add/', views.add_case_note, name='add_case_note'),
    path('cases/<int:case_id>/upload-document/', views.upload_case_document, name='upload_case_document'),
    path('departments/', views.department_list, name='department_list'),
    path('departments/create/', views.department_create, name='department_create'),
    path('departments/<int:department_id>/edit/', views.department_edit, name='department_edit'),
    path('departments/<int:department_id>/delete/', views.department_delete, name='department_delete'),

    # Dynamic case URLs
    path('dynamic/', dynamic_views.dynamic_index, name='dynamic_index'),
    path('dynamic/fields/', dynamic_views.field_definition_list, name='field_definition_list'),
    path('dynamic/fields/create/', dynamic_views.field_definition_create, name='field_definition_create'),
    path('dynamic/fields/<int:field_id>/edit/', dynamic_views.field_definition_edit, name='field_definition_edit'),
    path('dynamic/fields/<int:field_id>/delete/', dynamic_views.field_definition_delete, name='field_definition_delete'),
    path('dynamic/fields/initialize/', dynamic_views.initialize_case_fields, name='initialize_case_fields'),
    path('dynamic/cases/', dynamic_views.dynamic_case_list, name='dynamic_case_list'),
    path('dynamic/cases/create/', dynamic_views.dynamic_case_create, name='dynamic_case_create'),
    path('dynamic/cases/<int:case_id>/', dynamic_views.dynamic_case_detail, name='dynamic_case_detail'),
    path('dynamic/cases/<int:case_id>/edit/', dynamic_views.dynamic_case_edit, name='dynamic_case_edit'),
    path('dynamic/cases/<int:case_id>/delete/', dynamic_views.dynamic_case_delete, name='dynamic_case_delete'),
    path('dynamic/cases/<int:case_id>/notes/', dynamic_views.dynamic_case_notes, name='dynamic_case_notes'),
    path('dynamic/cases/<int:case_id>/notes/add/', dynamic_views.add_dynamic_case_note, name='add_dynamic_case_note'),
    path('dynamic/cases/<int:case_id>/upload-document/', dynamic_views.upload_dynamic_case_document, name='upload_dynamic_case_document'),
]
