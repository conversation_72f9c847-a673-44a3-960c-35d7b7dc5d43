# Generated by Django 5.2 on 2025-04-15 20:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('consultations', '0002_dynamicguarantee_dynamicguaranteedocument_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='fielddefinition',
            name='options',
            field=models.TextField(blank=True, help_text='للقوائم المنسدلة والاختيار المتعدد. افصل بين الخيارات بسطر جديد.', null=True, verbose_name='خيارات'),
        ),
        migrations.AlterField(
            model_name='fielddefinition',
            name='field_type',
            field=models.CharField(choices=[('text', 'نص'), ('number', 'رقم'), ('date', 'تاريخ'), ('boolean', 'نعم/لا'), ('select', 'قائمة منسدلة'), ('multi_select', 'اختيار متعدد')], max_length=20, verbose_name='نوع الحقل'),
        ),
    ]
