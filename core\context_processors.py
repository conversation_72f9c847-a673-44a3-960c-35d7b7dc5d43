from django.utils import timezone
from dashboard.models import Notification

def common_data(request):
    """
    Context processor to add common data to all templates
    """
    context = {
        'today': timezone.now().date(),
    }
    
    # Add unread notifications count for authenticated users
    if request.user.is_authenticated:
        unread_notifications_count = Notification.objects.filter(
            user=request.user, 
            is_read=False
        ).count()
        
        context['unread_notifications_count'] = unread_notifications_count
    
    return context
