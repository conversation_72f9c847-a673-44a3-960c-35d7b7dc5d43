{% extends 'base/base.html' %}

{% block title %}
{% if is_edit %}تعديل عضو{% else %}إضافة عضو جديد{% endif %} - هيأة المنافذ الحدودية
{% endblock %}

{% block page_title %}
{% if is_edit %}تعديل عضو{% else %}إضافة عضو جديد{% endif %}
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-3">
        <div class="col-md-12">
            <a href="{% url 'lawsuits:council_members' council.id %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة الأعضاء
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% if is_edit %}تعديل عضو{% else %}إضافة عضو جديد{% endif %}</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6>معلومات المجلس/اللجنة:</h6>
                        <p>
                            <strong>نوع المجلس/اللجنة:</strong> {{ council.get_council_type_display }}<br>
                            <strong>أمر التشكيل:</strong> {{ council.formation_order }}<br>
                            <strong>تاريخ التشكيل:</strong> {{ council.formation_date }}
                        </p>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم العضو <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{% if member %}{{ member.name }}{% endif %}" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="position" class="form-label">مقر العمل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="position" name="position" value="{% if member %}{{ member.position }}{% endif %}" required>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_head" name="is_head" {% if member and member.is_head %}checked{% endif %}>
                            <label class="form-check-label" for="is_head">رئيس اللجنة</label>
                            <div class="form-text">يمكن أن يكون هناك رئيس واحد فقط للجنة.</div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="{% url 'lawsuits:council_members' council.id %}" class="btn btn-secondary me-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">
                                {% if is_edit %}حفظ التغييرات{% else %}إضافة العضو{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
