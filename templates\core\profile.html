{% extends 'base/base.html' %}

{% block title %}الملف الشخصي - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}الملف الشخصي{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معلومات الملف الشخصي</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row mb-4">
                            <div class="col-md-4 text-center">
                                {% if profile.profile_picture %}
                                <img src="{{ profile.profile_picture.url }}" alt="الصورة الشخصية" class="img-fluid rounded-circle mb-3" style="max-width: 150px;">
                                {% else %}
                                <img src="https://via.placeholder.com/150" alt="الصورة الشخصية" class="img-fluid rounded-circle mb-3">
                                {% endif %}
                                
                                <div class="mb-3">
                                    <label for="profile_picture" class="form-label">تغيير الصورة الشخصية</label>
                                    <input type="file" class="form-control" id="profile_picture" name="profile_picture">
                                </div>
                            </div>
                            
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">الاسم الأول</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user.first_name }}" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">الاسم الأخير</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user.last_name }}" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="text" class="form-control" id="phone" name="phone" value="{{ profile.phone }}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="user_type" class="form-label">نوع المستخدم</label>
                                    <input type="text" class="form-control" id="user_type" value="{{ profile.get_user_type_display }}" readonly>
                                </div>
                                
                                {% if profile.department %}
                                <div class="mb-3">
                                    <label for="department" class="form-label">القسم</label>
                                    <input type="text" class="form-control" id="department" value="{{ profile.department.name }}" readonly>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
