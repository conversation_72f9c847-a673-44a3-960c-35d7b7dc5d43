from django.urls import path
from . import views
from . import dynamic_views

app_name = 'consultations'

urlpatterns = [
    path('', views.index, name='index'),

    # Legacy URLs - Redirected to new system
    path('old-guarantees/', views.redirect_to_dynamic_guarantees, name='guarantee_list'),
    path('old-guarantees/create/', views.redirect_to_dynamic_guarantee_create, name='guarantee_create'),
    path('old-guarantees/<int:guarantee_id>/', views.redirect_to_dynamic_guarantees, name='guarantee_detail'),
    path('old-guarantees/<int:guarantee_id>/edit/', views.redirect_to_dynamic_guarantees, name='guarantee_edit'),
    path('old-guarantees/<int:guarantee_id>/delete/', views.redirect_to_dynamic_guarantees, name='guarantee_delete'),
    path('old-guarantees/<int:guarantee_id>/upload-document/', views.redirect_to_dynamic_guarantees, name='upload_guarantee_document'),
    path('fields/', views.redirect_to_field_definitions, name='field_list'),
    path('fields/create/', views.redirect_to_field_definition_create, name='field_create'),
    path('fields/<int:field_id>/edit/', views.redirect_to_field_definitions, name='field_edit'),
    path('fields/<int:field_id>/delete/', views.redirect_to_field_definitions, name='field_delete'),

    # Field definitions
    path('field-definitions/', dynamic_views.field_definition_list, name='field_definition_list'),
    path('field-definitions/create/', dynamic_views.field_definition_create, name='field_definition_create'),
    path('field-definitions/<int:field_id>/edit/', dynamic_views.field_definition_edit, name='field_definition_edit'),
    path('field-definitions/<int:field_id>/delete/', dynamic_views.field_definition_delete, name='field_definition_delete'),
    path('field-definitions/initialize/', dynamic_views.initialize_field_definitions, name='initialize_field_definitions'),

    # Guarantees
    path('guarantees/', dynamic_views.dynamic_guarantee_list, name='dynamic_guarantee_list'),
    path('guarantees/create/', dynamic_views.dynamic_guarantee_create, name='dynamic_guarantee_create'),
    path('guarantees/export/', dynamic_views.export_guarantees_to_excel, name='export_guarantees_to_excel'),
    path('guarantees/<int:guarantee_id>/', dynamic_views.dynamic_guarantee_detail, name='dynamic_guarantee_detail'),
    path('guarantees/<int:guarantee_id>/edit/', dynamic_views.dynamic_guarantee_edit, name='dynamic_guarantee_edit'),
    path('guarantees/<int:guarantee_id>/delete/', dynamic_views.dynamic_guarantee_delete, name='dynamic_guarantee_delete'),
    path('guarantees/<int:guarantee_id>/upload-document/', dynamic_views.upload_dynamic_guarantee_document, name='upload_dynamic_guarantee_document'),
]
