{% extends 'base/base.html' %}

{% load consultations_extras %}

{% block title %}
{% if is_edit %}تعديل كفالة{% else %}إضافة كفالة جديدة{% endif %} - هيأة المنافذ الحدودية
{% endblock %}

{% block page_title %}
{% if is_edit %}تعديل كفالة{% else %}إضافة كفالة جديدة{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-12">
            <a href="{% if is_edit %}{% url 'consultations:guarantee_detail' guarantee.id %}{% else %}{% url 'consultations:guarantee_list' %}{% endif %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i> العودة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% if is_edit %}تعديل كفالة{% else %}إضافة كفالة جديدة{% endif %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">المعلومات الأساسية</h5>

                                <div class="mb-3">
                                    <label for="guarantor_name" class="form-label">اسم الكفيل <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="guarantor_name" name="guarantor_name" value="{% if guarantee %}{{ guarantee.guarantor_name }}{% endif %}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="guaranteed_name" class="form-label">اسم المكفول <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="guaranteed_name" name="guaranteed_name" value="{% if guarantee %}{{ guarantee.guaranteed_name }}{% endif %}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="guarantee_duration" class="form-label">مدة الكفالة <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="guarantee_duration" name="guarantee_duration" value="{% if guarantee %}{{ guarantee.guarantee_duration }}{% endif %}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="guarantee_amount" class="form-label">مبلغ الكفالة <span class="text-danger">*</span></label>
                                    <input type="number" step="0.01" class="form-control" id="guarantee_amount" name="guarantee_amount" value="{% if guarantee %}{{ guarantee.guarantee_amount }}{% endif %}" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h5 class="mb-3">معلومات إضافية</h5>

                                <div class="mb-3">
                                    <label for="bank_name" class="form-label">اسم المصرف المانح <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="bank_name" name="bank_name" value="{% if guarantee %}{{ guarantee.bank_name }}{% endif %}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="issue_date" class="form-label">تاريخ منح الكفالة <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="issue_date" name="issue_date" value="{% if guarantee %}{{ guarantee.issue_date|date:'Y-m-d' }}{% endif %}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="expiry_date" class="form-label">تاريخ انتهاء الكفالة <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="expiry_date" name="expiry_date" value="{% if guarantee %}{{ guarantee.expiry_date|date:'Y-m-d' }}{% endif %}" required>
                                </div>

                                {% if is_edit %}
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {% if guarantee.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">فعالة</label>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Custom Fields are now integrated with the main form sections -->
                        {% if custom_fields %}
                        <!-- Custom fields are distributed between Basic Information and Additional Information sections -->
                        {% for field in custom_fields %}
                        {% if forloop.counter0|divisibleby:2 %}
                        <!-- Add to Basic Information section -->
                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                // Find the Basic Information section
                                const basicInfoSection = document.querySelector('.col-md-6:first-child');

                                // Create the field element
                                const fieldElement = document.createElement('div');
                                fieldElement.className = 'mb-3';
                                fieldElement.innerHTML = `
                                    <label for="field_{{ field.id }}" class="form-label">
                                        {{ field.name }}
                                        {% if field.is_required %}<span class="text-danger">*</span>{% endif %}
                                    </label>

                                    {% if field.field_type == 'text' %}
                                    <input type="text" class="form-control" id="field_{{ field.id }}" name="field_{{ field.id }}" value="{% if field_values and field.id in field_values %}{{ field_values|get_item:field.id }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'number' %}
                                    <input type="number" class="form-control" id="field_{{ field.id }}" name="field_{{ field.id }}" value="{% if field_values and field.id in field_values %}{{ field_values|get_item:field.id }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'date' %}
                                    <input type="date" class="form-control" id="field_{{ field.id }}" name="field_{{ field.id }}" value="{% if field_values and field.id in field_values %}{{ field_values|get_item:field.id }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'boolean' %}
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="field_{{ field.id }}" name="field_{{ field.id }}" value="true" {% if field_values and field.id in field_values and field_values|get_item:field.id == 'true' %}checked{% endif %}>
                                        <label class="form-check-label" for="field_{{ field.id }}">نعم</label>
                                    </div>
                                    {% endif %}
                                `;

                                // Append the field to the Basic Information section
                                basicInfoSection.appendChild(fieldElement);
                            });
                        </script>
                        {% else %}
                        <!-- Add to Additional Information section -->
                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                // Find the Additional Information section
                                const additionalInfoSection = document.querySelector('.col-md-6:nth-child(2)');

                                // Create the field element
                                const fieldElement = document.createElement('div');
                                fieldElement.className = 'mb-3';
                                fieldElement.innerHTML = `
                                    <label for="field_{{ field.id }}" class="form-label">
                                        {{ field.name }}
                                        {% if field.is_required %}<span class="text-danger">*</span>{% endif %}
                                    </label>

                                    {% if field.field_type == 'text' %}
                                    <input type="text" class="form-control" id="field_{{ field.id }}" name="field_{{ field.id }}" value="{% if field_values and field.id in field_values %}{{ field_values|get_item:field.id }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'number' %}
                                    <input type="number" class="form-control" id="field_{{ field.id }}" name="field_{{ field.id }}" value="{% if field_values and field.id in field_values %}{{ field_values|get_item:field.id }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'date' %}
                                    <input type="date" class="form-control" id="field_{{ field.id }}" name="field_{{ field.id }}" value="{% if field_values and field.id in field_values %}{{ field_values|get_item:field.id }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'boolean' %}
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="field_{{ field.id }}" name="field_{{ field.id }}" value="true" {% if field_values and field.id in field_values and field_values|get_item:field.id == 'true' %}checked{% endif %}>
                                        <label class="form-check-label" for="field_{{ field.id }}">نعم</label>
                                    </div>
                                    {% endif %}
                                `;

                                // Append the field to the Additional Information section
                                additionalInfoSection.appendChild(fieldElement);
                            });
                        </script>
                        {% endif %}
                        {% endfor %}
                        {% endif %}

                        <!-- Document Upload (only for new guarantees) -->
                        {% if not is_edit %}
                        <hr>
                        <h5 class="mb-3">رفع كتاب الكفالة</h5>
                        <div class="mb-3">
                            <label for="guarantee_letter" class="form-label">كتاب الكفالة</label>
                            <input type="file" class="form-control" id="guarantee_letter" name="guarantee_letter">
                            <div class="form-text">يمكنك رفع كتاب الكفالة الآن أو لاحقاً من صفحة تفاصيل الكفالة.</div>
                        </div>
                        {% endif %}

                        <div class="text-center mt-4">
                            <a href="{% if is_edit %}{% url 'consultations:guarantee_detail' guarantee.id %}{% else %}{% url 'consultations:guarantee_list' %}{% endif %}" class="btn btn-secondary me-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">
                                {% if is_edit %}حفظ التغييرات{% else %}إضافة الكفالة{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
