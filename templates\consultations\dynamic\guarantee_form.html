{% extends 'base/base.html' %}
{% load consultations_extras %}

{% block title %}
{% if is_edit %}تعديل كفالة{% else %}إضافة كفالة جديدة{% endif %} - هيأة المنافذ الحدودية
{% endblock %}

{% block page_title %}
{% if is_edit %}تعديل كفالة{% else %}إضافة كفالة جديدة{% endif %}
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-3">
        <div class="col-md-12">
            <a href="{% if is_edit %}{% url 'consultations:dynamic_guarantee_detail' guarantee.id %}{% else %}{% url 'consultations:dynamic_guarantee_list' %}{% endif %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i> العودة
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{% if is_edit %}تعديل كفالة{% else %}إضافة كفالة جديدة{% endif %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">المعلومات الأساسية</h5>

                                {% for field in basic_fields %}
                                <div class="mb-3">
                                    <label for="field_{{ field.field_key }}" class="form-label">
                                        {{ field.name }}
                                        {% if field.is_required %}<span class="text-danger">*</span>{% endif %}
                                    </label>

                                    {% if field.field_type == 'text' %}
                                    <input type="text" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" value="{% if field_values and field.field_key in field_values %}{{ field_values|get_item:field.field_key }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'number' %}
                                    <input type="number" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" value="{% if field_values and field.field_key in field_values %}{{ field_values|get_item:field.field_key }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'date' %}
                                    <input type="date" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" value="{% if field_values and field.field_key in field_values %}{{ field_values|get_item:field.field_key }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'boolean' %}
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" value="true" {% if field_values and field.field_key in field_values and field_values|get_item:field.field_key == 'true' %}checked{% endif %}>
                                        <label class="form-check-label" for="field_{{ field.field_key }}">نعم</label>
                                    </div>

                                    {% elif field.field_type == 'select' %}
                                    <select class="form-select" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" {% if field.is_required %}required{% endif %}>
                                        <option value="" selected disabled>اختر...</option>
                                        {% if field.options %}
                                            {% for option in field.options.splitlines %}
                                                <option value="{{ option }}" {% if field_values and field.field_key in field_values and field_values|get_item:field.field_key == option %}selected{% endif %}>{{ option }}</option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>

                                    {% elif field.field_type == 'multi_select' %}
                                    <select class="form-select" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" multiple {% if field.is_required %}required{% endif %}>
                                        {% if field.options %}
                                            {% for option in field.options.splitlines %}
                                                <option value="{{ option }}" {% if field_values and field.field_key in field_values and option in field_values|get_item:field.field_key %}selected{% endif %}>{{ option }}</option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                    <div class="form-text">يمكنك اختيار أكثر من خيار بالضغط على Ctrl أثناء النقر</div>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>

                            <!-- Additional Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">معلومات إضافية</h5>

                                {% for field in additional_fields %}
                                <div class="mb-3">
                                    <label for="field_{{ field.field_key }}" class="form-label">
                                        {{ field.name }}
                                        {% if field.is_required %}<span class="text-danger">*</span>{% endif %}
                                    </label>

                                    {% if field.field_type == 'text' %}
                                    <input type="text" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" value="{% if field_values and field.field_key in field_values %}{{ field_values|get_item:field.field_key }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'number' %}
                                    <input type="number" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" value="{% if field_values and field.field_key in field_values %}{{ field_values|get_item:field.field_key }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'date' %}
                                    <input type="date" class="form-control" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" value="{% if field_values and field.field_key in field_values %}{{ field_values|get_item:field.field_key }}{% endif %}" {% if field.is_required %}required{% endif %}>

                                    {% elif field.field_type == 'boolean' %}
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" value="true" {% if field_values and field.field_key in field_values and field_values|get_item:field.field_key == 'true' %}checked{% endif %}>
                                        <label class="form-check-label" for="field_{{ field.field_key }}">نعم</label>
                                    </div>

                                    {% elif field.field_type == 'select' %}
                                    <select class="form-select" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" {% if field.is_required %}required{% endif %}>
                                        <option value="" selected disabled>اختر...</option>
                                        {% if field.options %}
                                            {% for option in field.options.splitlines %}
                                                <option value="{{ option }}" {% if field_values and field.field_key in field_values and field_values|get_item:field.field_key == option %}selected{% endif %}>{{ option }}</option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>

                                    {% elif field.field_type == 'multi_select' %}
                                    <select class="form-select" id="field_{{ field.field_key }}" name="field_{{ field.field_key }}" multiple {% if field.is_required %}required{% endif %}>
                                        {% if field.options %}
                                            {% for option in field.options.splitlines %}
                                                <option value="{{ option }}" {% if field_values and field.field_key in field_values and option in field_values|get_item:field.field_key %}selected{% endif %}>{{ option }}</option>
                                            {% endfor %}
                                        {% endif %}
                                    </select>
                                    <div class="form-text">يمكنك اختيار أكثر من خيار بالضغط على Ctrl أثناء النقر</div>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Document Upload (only for new guarantees) -->
                        {% if not is_edit %}
                        <hr>
                        <h5 class="mb-3">رفع المستندات</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="guarantee_letter" class="form-label">كتاب الكفالة</label>
                                    <input type="file" class="form-control" id="guarantee_letter" name="guarantee_letter">
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if is_edit %}
                        <hr>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {% if guarantee.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">فعالة</label>
                        </div>
                        {% endif %}

                        <div class="text-center mt-4">
                            <a href="{% if is_edit %}{% url 'consultations:dynamic_guarantee_detail' guarantee.id %}{% else %}{% url 'consultations:dynamic_guarantee_list' %}{% endif %}" class="btn btn-secondary me-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">
                                {% if is_edit %}حفظ التغييرات{% else %}إضافة الكفالة{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
