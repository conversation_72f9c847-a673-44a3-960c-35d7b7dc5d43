{% extends 'base/base.html' %}

{% block title %}قائمة المجالس/اللجان - هيأة المنافذ الحدودية{% endblock %}

{% block page_title %}قائمة المجالس/اللجان{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">فلاتر البحث</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">بحث</label>
                    <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="أمر التشكيل، المنفذ، الموضوع...">
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {% if status_filter == 'all' %}selected{% endif %}>الكل</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>فعالة</option>
                        <option value="closed" {% if status_filter == 'closed' %}selected{% endif %}>مغلقة</option>
                        <option value="expired" {% if status_filter == 'expired' %}selected{% endif %}>متجاوزة للمدة</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="council_type" class="form-label">نوع المجلس/اللجنة</label>
                    <select class="form-select" id="council_type" name="council_type">
                        <option value="">الكل</option>
                        {% for type_code, type_name in council_types %}
                        <option value="{{ type_code }}" {% if council_type_filter == type_code %}selected{% endif %}>{{ type_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
                </div>
                
                <div class="col-md-2">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
                </div>
                
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Councils Table -->
    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">المجالس/اللجان</h5>
            <a href="{% url 'lawsuits:council_create' %}" class="btn btn-light btn-sm">
                <i class="fas fa-plus-circle"></i> إضافة مجلس/لجنة جديدة
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <a href="?{% if 'council_type' in order_by %}-{% endif %}council_type=&search={{ search_query }}&status={{ status_filter }}&council_type={{ council_type_filter }}&start_date={{ start_date }}&end_date={{ end_date }}">
                                    نوع المجلس/اللجنة
                                    {% if order_by == 'council_type' %}
                                    <i class="fas fa-sort-up"></i>
                                    {% elif order_by == '-council_type' %}
                                    <i class="fas fa-sort-down"></i>
                                    {% else %}
                                    <i class="fas fa-sort"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?{% if 'formation_order' in order_by %}-{% endif %}formation_order=&search={{ search_query }}&status={{ status_filter }}&council_type={{ council_type_filter }}&start_date={{ start_date }}&end_date={{ end_date }}">
                                    أمر التشكيل
                                    {% if order_by == 'formation_order' %}
                                    <i class="fas fa-sort-up"></i>
                                    {% elif order_by == '-formation_order' %}
                                    <i class="fas fa-sort-down"></i>
                                    {% else %}
                                    <i class="fas fa-sort"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?{% if 'formation_date' in order_by %}-{% endif %}formation_date=&search={{ search_query }}&status={{ status_filter }}&council_type={{ council_type_filter }}&start_date={{ start_date }}&end_date={{ end_date }}">
                                    تاريخ التشكيل
                                    {% if order_by == 'formation_date' %}
                                    <i class="fas fa-sort-up"></i>
                                    {% elif order_by == '-formation_date' %}
                                    <i class="fas fa-sort-down"></i>
                                    {% else %}
                                    <i class="fas fa-sort"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?{% if 'port_name' in order_by %}-{% endif %}port_name=&search={{ search_query }}&status={{ status_filter }}&council_type={{ council_type_filter }}&start_date={{ start_date }}&end_date={{ end_date }}">
                                    المنفذ/الدائرة
                                    {% if order_by == 'port_name' %}
                                    <i class="fas fa-sort-up"></i>
                                    {% elif order_by == '-port_name' %}
                                    <i class="fas fa-sort-down"></i>
                                    {% else %}
                                    <i class="fas fa-sort"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?{% if 'duration' in order_by %}-{% endif %}duration=&search={{ search_query }}&status={{ status_filter }}&council_type={{ council_type_filter }}&start_date={{ start_date }}&end_date={{ end_date }}">
                                    المدة (أيام)
                                    {% if order_by == 'duration' %}
                                    <i class="fas fa-sort-up"></i>
                                    {% elif order_by == '-duration' %}
                                    <i class="fas fa-sort-down"></i>
                                    {% else %}
                                    <i class="fas fa-sort"></i>
                                    {% endif %}
                                </a>
                            </th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for council in page_obj %}
                        <tr>
                            <td>{{ council.get_council_type_display }}</td>
                            <td>{{ council.formation_order }}</td>
                            <td>{{ council.formation_date }}</td>
                            <td>{{ council.port_name }}</td>
                            <td>{{ council.duration }}{% if council.extended %} + {{ council.extension_days }}{% endif %}</td>
                            <td>
                                {% if council.is_closed %}
                                    <span class="badge bg-secondary">مغلق</span>
                                {% else %}
                                    {% if council.is_expired %}
                                        <span class="badge bg-danger">متجاوز للمدة</span>
                                    {% else %}
                                        <span class="badge bg-success">فعال</span>
                                    {% endif %}
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'lawsuits:council_detail' council.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'lawsuits:council_edit' council.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'lawsuits:council_delete' council.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">لا توجد مجالس/لجان مطابقة لمعايير البحث</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-4">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1&search={{ search_query }}&status={{ status_filter }}&council_type={{ council_type_filter }}&start_date={{ start_date }}&end_date={{ end_date }}&order_by={{ order_by }}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}&search={{ search_query }}&status={{ status_filter }}&council_type={{ council_type_filter }}&start_date={{ start_date }}&end_date={{ end_date }}&order_by={{ order_by }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                        <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                        {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ i }}&search={{ search_query }}&status={{ status_filter }}&council_type={{ council_type_filter }}&start_date={{ start_date }}&end_date={{ end_date }}&order_by={{ order_by }}">{{ i }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}&search={{ search_query }}&status={{ status_filter }}&council_type={{ council_type_filter }}&start_date={{ start_date }}&end_date={{ end_date }}&order_by={{ order_by }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}&search={{ search_query }}&status={{ status_filter }}&council_type={{ council_type_filter }}&start_date={{ start_date }}&end_date={{ end_date }}&order_by={{ order_by }}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
{% endblock %}
{% endblock %}
